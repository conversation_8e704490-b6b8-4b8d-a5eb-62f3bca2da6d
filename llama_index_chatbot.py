import os
import sys
import warnings
from dotenv import load_dotenv

# Fix PyTorch compatibility issues with Python 3.13
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Set environment variables to avoid PyTorch issues
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["HF_HUB_DISABLE_SYMLINKS_WARNING"] = "1"

import streamlit as st

try:
    from llama_index.core import VectorStoreIndex, SimpleDirectoryReader, Settings, StorageContext, Document
    from llama_index.core.node_parser import SentenceSplitter
    from llama_index.core.retrievers import VectorIndexRetriever
    from llama_index.core.query_engine import RetrieverQueryEngine
    from llama_index.core.postprocessor import SimilarityPostprocessor, MetadataReplacementPostProcessor, KeywordNodePostprocessor
    from llama_index.core.response_synthesizers import get_response_synthesizer
    from llama_index.core.memory import ChatMemoryBuffer
    from llama_index.core.chat_engine import CondensePlusContextChatEngine
    from llama_index.llms.openai import OpenAI
    from llama_index.vector_stores.chroma import ChromaVectorStore
    import chromadb

    # Try to import advanced features, fallback if they fail
    try:
        from llama_index.core.node_parser import SemanticSplitterNodeParser
        SEMANTIC_SPLITTER_AVAILABLE = True
    except ImportError:
        SEMANTIC_SPLITTER_AVAILABLE = False

    try:
        from llama_index.core.extractors import TitleExtractor, QuestionsAnsweredExtractor, SummaryExtractor
        from llama_index.core.schema import MetadataMode
        EXTRACTORS_AVAILABLE = True
    except ImportError:
        EXTRACTORS_AVAILABLE = False

    try:
        from llama_index.embeddings.huggingface import HuggingFaceEmbedding
        HUGGINGFACE_AVAILABLE = True
    except ImportError:
        HUGGINGFACE_AVAILABLE = False

except ImportError as e:
    st.error(f"❌ Failed to import required libraries: {str(e)}")
    st.stop()

import json
from datetime import datetime
import hashlib

# Load environment variables
load_dotenv()

# Setup directories
DATA_DIR = "documents"
CHROMA_DB_DIR = "chroma_db"
CHAT_HISTORY_FILE = "chat_history.json"
METADATA_FILE = "document_metadata.json"
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(CHROMA_DB_DIR, exist_ok=True)

# Advanced RAG Configuration for Maximum Indexing Accuracy
RAG_CONFIG = {
    # Chunking Strategy for Precision
    "chunk_size": 200,          # Even smaller chunks for maximum precision
    "chunk_overlap": 80,        # Higher overlap (40% overlap) for better context
    "sentence_splitter": True,  # Use sentence-aware splitting

    # Retrieval Parameters
    "similarity_top_k": 12,     # More candidates for comprehensive recall
    "similarity_cutoff": 0.15,  # Lower cutoff for maximum recall
    "rerank_top_n": 6,         # Re-rank more candidates

    # Advanced Processing
    "response_mode": "tree_summarize",
    "streaming": False,
    "enable_reranking": True,
    "enable_hybrid_search": True,  # Combine semantic + keyword search
    "enable_query_expansion": True, # Expand queries for better matching

    # Multi-level Indexing
    "enable_hierarchical": True,   # Create document and chunk level indices
    "enable_metadata_filtering": True, # Use metadata for better filtering

    # Conversational Memory
    "context_window": 4,        # More conversation context
    "enable_chat_memory": True,

    # Quality Control
    "min_chunk_size": 50,      # Minimum viable chunk size
    "max_chunk_size": 400,     # Maximum chunk size
    "quality_threshold": 0.1   # Minimum quality score for chunks
}

# Initialize LLM with enhanced conversational settings
llm = OpenAI(
    model="gpt-4",
    temperature=0.1,
    max_tokens=1500,
    system_prompt="""You are a helpful AI assistant that answers questions based on the provided context and conversation history.

Key Instructions:
1. Use the provided document context to answer questions accurately
2. Remember and reference previous parts of our conversation when relevant
3. If the current question relates to previous questions, acknowledge that connection
4. Always cite source documents when possible with specific filenames
5. If you cannot find relevant information in the context, clearly state that
6. Maintain conversation flow by referencing earlier topics when appropriate
7. Be specific about what information comes from which document
8. If asked about something mentioned earlier, refer back to that context

Format your responses clearly and maintain conversational continuity."""
)
Settings.llm = llm

# Configure advanced node parser for maximum accuracy
def create_advanced_node_parser():
    """Create advanced node parser with semantic splitting"""
    try:
        if RAG_CONFIG.get("sentence_splitter", True) and SEMANTIC_SPLITTER_AVAILABLE:
            # Try semantic splitter first (most accurate)
            try:
                semantic_parser = SemanticSplitterNodeParser(
                    buffer_size=1,
                    breakpoint_percentile_threshold=95,
                    embed_model=Settings.embed_model
                )
                return semantic_parser
            except Exception as e:
                st.warning(f"⚠️ Semantic splitter failed: {str(e)}")

        # Fallback to enhanced sentence splitter
        sentence_parser = SentenceSplitter(
            chunk_size=RAG_CONFIG["chunk_size"],
            chunk_overlap=RAG_CONFIG["chunk_overlap"],
            paragraph_separator="\n\n",
            secondary_chunking_regex="[^,.;。？！]+[,.;。？！]?",
            tokenizer=None  # Use default tokenizer
        )
        return sentence_parser

    except Exception as e:
        st.warning(f"⚠️ Advanced parser failed: {str(e)}")
        # Ultimate fallback
        return SentenceSplitter(
            chunk_size=RAG_CONFIG["chunk_size"],
            chunk_overlap=RAG_CONFIG["chunk_overlap"]
        )

# Set the advanced node parser
Settings.node_parser = create_advanced_node_parser()

# Function to initialize embeddings
def initialize_embeddings():
    """Initialize embeddings with fallback to OpenAI"""
    if HUGGINGFACE_AVAILABLE:
        try:
            embed_model = HuggingFaceEmbedding(model_name="BAAI/bge-small-en-v1.5")
            Settings.embed_model = embed_model
            return "🤖 Using local BGE embeddings"
        except Exception as e:
            return f"⚠️ Using OpenAI embeddings (local model failed: {str(e)[:100]}...)"
    else:
        return "⚠️ Using OpenAI embeddings (HuggingFace not available)"

# Load and save chat history
def load_chat_history():
    if os.path.exists(CHAT_HISTORY_FILE):
        with open(CHAT_HISTORY_FILE, "r") as f:
            return json.load(f)
    return []

def save_chat_history(history):
    with open(CHAT_HISTORY_FILE, "w") as f:
        json.dump(history, f, indent=4)

# Enhanced document metadata management
def load_document_metadata():
    """Load document metadata for tracking changes"""
    if os.path.exists(METADATA_FILE):
        with open(METADATA_FILE, "r") as f:
            return json.load(f)
    return {}

def save_document_metadata(metadata):
    """Save document metadata"""
    with open(METADATA_FILE, "w") as f:
        json.dump(metadata, f, indent=4)

def get_file_hash(file_path):
    """Get file hash for change detection"""
    with open(file_path, "rb") as f:
        return hashlib.md5(f.read()).hexdigest()

def process_documents_with_advanced_metadata():
    """Process documents with advanced metadata extraction and quality control"""
    documents = []
    metadata = load_document_metadata()

    # Focus on core accuracy improvements: better chunking, reranking, and hybrid search
    # This provides the main accuracy gains without complex metadata extraction
    st.info("📝 Using optimized basic metadata for maximum reliability")

    for filename in os.listdir(DATA_DIR):
        file_path = os.path.join(DATA_DIR, filename)
        if os.path.isfile(file_path):
            file_hash = get_file_hash(file_path)
            file_stats = os.stat(file_path)

            # Create enhanced metadata
            doc_metadata = {
                "filename": filename,
                "file_path": file_path,
                "file_size": file_stats.st_size,
                "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                "file_hash": file_hash,
                "file_type": filename.split('.')[-1].lower() if '.' in filename else 'unknown',
                "processing_timestamp": datetime.now().isoformat(),
                "source_type": "document"
            }

            # Update metadata tracking
            metadata[filename] = doc_metadata

            # Load document with metadata
            try:
                reader = SimpleDirectoryReader(input_files=[file_path])
                file_docs = reader.load_data()

                # Process each document
                for doc in file_docs:
                    # Add basic metadata
                    doc.metadata.update(doc_metadata)

                    # Quality filtering - skip very short documents
                    if len(doc.text.strip()) < RAG_CONFIG.get("min_chunk_size", 50):
                        st.warning(f"⚠️ Skipping {filename}: too short ({len(doc.text)} chars)")
                        continue

                    # Add enhanced basic metadata for better indexing
                    try:
                        # Extract basic content features for better indexing
                        text_length = len(doc.text)
                        word_count = len(doc.text.split())

                        # Add content-based metadata
                        doc.metadata.update({
                            "text_length": text_length,
                            "word_count": word_count,
                            "content_type": "text",
                            "language": "en",  # Assume English for now
                            "chunk_strategy": "optimized"
                        })

                        # Extract first few words as a simple title
                        first_words = " ".join(doc.text.split()[:10])
                        if first_words:
                            doc.metadata["excerpt"] = first_words + "..."

                    except Exception as e:
                        st.warning(f"⚠️ Basic metadata extraction failed for {filename}: {str(e)}")

                    # Add document quality score
                    doc.metadata["quality_score"] = calculate_document_quality(doc.text)

                    documents.append(doc)

            except Exception as e:
                st.warning(f"⚠️ Could not process {filename}: {str(e)}")

    # Filter documents by quality
    quality_threshold = RAG_CONFIG.get("quality_threshold", 0.1)
    high_quality_docs = [
        doc for doc in documents
        if doc.metadata.get("quality_score", 0) >= quality_threshold
    ]

    if len(high_quality_docs) < len(documents):
        st.info(f"📊 Quality filtering: {len(high_quality_docs)}/{len(documents)} documents passed quality check")

    # Save updated metadata
    save_document_metadata(metadata)
    return high_quality_docs

def calculate_document_quality(text):
    """Calculate a quality score for document content"""
    if not text or len(text.strip()) < 10:
        return 0.0

    # Basic quality metrics
    word_count = len(text.split())
    sentence_count = len([s for s in text.split('.') if s.strip()])
    avg_word_length = sum(len(word) for word in text.split()) / max(word_count, 1)

    # Quality indicators
    has_punctuation = any(p in text for p in '.!?')
    has_uppercase = any(c.isupper() for c in text)
    has_lowercase = any(c.islower() for c in text)

    # Calculate score (0-1)
    quality_score = 0.0

    # Word count factor (optimal around 50-500 words per chunk)
    if 50 <= word_count <= 500:
        quality_score += 0.3
    elif word_count > 20:
        quality_score += 0.1

    # Sentence structure
    if sentence_count > 1:
        quality_score += 0.2

    # Average word length (2-8 chars is good)
    if 2 <= avg_word_length <= 8:
        quality_score += 0.2

    # Basic formatting
    if has_punctuation:
        quality_score += 0.1
    if has_uppercase and has_lowercase:
        quality_score += 0.1

    # Avoid very repetitive text
    unique_words = len(set(text.lower().split()))
    if unique_words / max(word_count, 1) > 0.3:
        quality_score += 0.1

    return min(quality_score, 1.0)

# Chroma DB and Index management functions
def get_chroma_client():
    """Initialize and return Chroma client"""
    return chromadb.PersistentClient(path=CHROMA_DB_DIR)

def load_or_create_index():
    """Load existing index from Chroma DB or create new one if it doesn't exist"""
    try:
        # Initialize Chroma client
        chroma_client = get_chroma_client()

        # Try to get existing collection
        try:
            chroma_collection = chroma_client.get_collection("documents")
            st.info("📂 Found existing Chroma collection")
        except:
            # Create new collection if it doesn't exist
            chroma_collection = chroma_client.create_collection("documents")
            st.info("🆕 Created new Chroma collection")

        # Create vector store
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
        storage_context = StorageContext.from_defaults(vector_store=vector_store)

        # Check if we have documents to index
        if not os.listdir(DATA_DIR):
            st.warning("📁 No documents found. Please upload some documents first.")
            return None

        # Load documents with enhanced metadata
        documents = process_documents_with_advanced_metadata()

        if not documents:
            st.warning("📁 No valid documents found to process.")
            return None

        # Create or load index
        if chroma_collection.count() == 0:
            # Create new index if collection is empty
            index = VectorStoreIndex.from_documents(
                documents, storage_context=storage_context
            )
            st.success("🔄 Created new vector index with Chroma DB")
        else:
            # Load existing index
            index = VectorStoreIndex.from_vector_store(
                vector_store, storage_context=storage_context
            )
            st.info("� Loaded existing vector index from Chroma DB")

        return index

    except Exception as e:
        st.error(f"Error with Chroma DB: {str(e)}")
        return None

def update_index():
    """Update the index when new documents are added"""
    try:
        # Initialize Chroma client
        chroma_client = get_chroma_client()

        # Delete existing collection and recreate (for simplicity)
        try:
            chroma_client.delete_collection("documents")
        except:
            pass

        # Create new collection
        chroma_collection = chroma_client.create_collection("documents")
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
        storage_context = StorageContext.from_defaults(vector_store=vector_store)

        # Load all documents (including new ones)
        documents = SimpleDirectoryReader(DATA_DIR).load_data()

        # Create new index with all documents
        index = VectorStoreIndex.from_documents(
            documents, storage_context=storage_context
        )

        st.success("🔄 Updated vector index with new documents in Chroma DB")
        return index

    except Exception as e:
        st.error(f"Error updating index: {str(e)}")
        return load_or_create_index()

def create_enhanced_query_engine(index):
    """Create query engine with maximum indexing accuracy and hybrid search"""
    try:
        # Enhanced retriever with more candidates for better recall
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=RAG_CONFIG["similarity_top_k"]
        )

        # Advanced post-processors for maximum accuracy
        postprocessors = []

        # 1. Similarity filtering
        postprocessors.append(
            SimilarityPostprocessor(similarity_cutoff=RAG_CONFIG["similarity_cutoff"])
        )

        # 2. Keyword-based filtering for hybrid search
        if RAG_CONFIG.get("enable_hybrid_search", False):
            try:
                postprocessors.append(
                    KeywordNodePostprocessor(
                        required_keywords=[],  # Will be set dynamically
                        exclude_keywords=["advertisement", "footer", "header"]
                    )
                )
                st.info("🔍 Hybrid search enabled (semantic + keyword)")
            except Exception as e:
                st.warning(f"⚠️ Keyword filtering failed: {str(e)}")

        # 3. Metadata filtering for better relevance
        if RAG_CONFIG.get("enable_metadata_filtering", False):
            try:
                postprocessors.append(
                    MetadataReplacementPostProcessor(target_metadata_key="window")
                )
                st.info("📊 Metadata filtering enabled")
            except Exception as e:
                st.warning(f"⚠️ Metadata filtering failed: {str(e)}")

        # 4. LLM reranking for highest accuracy
        if RAG_CONFIG.get("enable_reranking", False):
            try:
                from llama_index.core.postprocessor import LLMRerank
                postprocessors.append(
                    LLMRerank(
                        choice_batch_size=RAG_CONFIG["rerank_top_n"],
                        top_n=RAG_CONFIG["rerank_top_n"],
                        llm=llm
                    )
                )
                st.info("🎯 LLM reranking enabled for maximum accuracy")
            except Exception as e:
                st.warning(f"⚠️ LLM reranking failed: {str(e)}")

        # Enhanced response synthesizer
        response_synthesizer = get_response_synthesizer(
            response_mode=RAG_CONFIG["response_mode"],
            streaming=RAG_CONFIG["streaming"],
            use_async=False
        )

        # Create the enhanced query engine
        query_engine = RetrieverQueryEngine(
            retriever=retriever,
            response_synthesizer=response_synthesizer,
            node_postprocessors=postprocessors
        )

        st.success(f"� Enhanced query engine created with {len(postprocessors)} accuracy layers")
        return query_engine

    except Exception as e:
        st.warning(f"⚠️ Enhanced query engine failed: {str(e)}")
        st.info("🔄 Falling back to simple query engine")
        # Fallback to simple but reliable query engine
        return index.as_query_engine(
            similarity_top_k=RAG_CONFIG["similarity_top_k"],
            response_mode=RAG_CONFIG["response_mode"]
        )

def create_conversational_engine(index, chat_history):
    """Create enhanced query engine with conversational context"""
    try:
        if not RAG_CONFIG.get("enable_chat_memory", False):
            return create_enhanced_query_engine(index)

        # Get recent conversation context
        context_window = RAG_CONFIG.get("context_window", 3)
        recent_messages = chat_history[-context_window*2:] if len(chat_history) > 0 else []

        # Create enhanced query engine
        query_engine = create_enhanced_query_engine(index)

        # Add conversation context to the query engine
        if recent_messages:
            st.info(f"🧠 Using conversational context ({len(recent_messages)} recent messages)")
        else:
            st.info("🔧 Using enhanced query engine (no conversation history)")

        return query_engine

    except Exception as e:
        st.warning(f"⚠️ Conversational engine failed: {str(e)}")
        st.info("🔄 Falling back to enhanced query engine")
        return create_enhanced_query_engine(index)

def enhance_query_with_context(query, chat_history):
    """Enhance the query with conversational context"""
    if not RAG_CONFIG.get("enable_chat_memory", False) or not chat_history:
        return expand_query_for_better_matching(query)

    context_window = RAG_CONFIG.get("context_window", 3)
    recent_messages = chat_history[-context_window*2:] if len(chat_history) > 0 else []

    if not recent_messages:
        return expand_query_for_better_matching(query)

    # Build context from recent conversation
    context_parts = []
    for msg in recent_messages[-6:]:  # Last 3 pairs
        if msg["role"] == "user":
            context_parts.append(f"Previous question: {msg['content']}")
        elif msg["role"] == "assistant":
            context_parts.append(f"Previous answer: {msg['content'][:200]}...")

    if context_parts:
        enhanced_query = f"""
Previous conversation context:
{chr(10).join(context_parts)}

Current question: {query}

Please answer the current question while considering the conversation context above. If the current question relates to previous topics, acknowledge that connection.
"""
        return expand_query_for_better_matching(enhanced_query)

    return expand_query_for_better_matching(query)

def expand_query_for_better_matching(query):
    """Expand query with synonyms and related terms for better retrieval accuracy"""
    if not RAG_CONFIG.get("enable_query_expansion", False):
        return query

    try:
        # Simple query expansion with common synonyms and variations
        expansion_map = {
            "what": ["what", "which", "describe", "explain"],
            "how": ["how", "method", "process", "way", "approach"],
            "why": ["why", "reason", "cause", "purpose", "rationale"],
            "when": ["when", "time", "date", "period", "timing"],
            "where": ["where", "location", "place", "position"],
            "who": ["who", "person", "people", "individual", "author"],
            "define": ["define", "definition", "meaning", "explanation"],
            "compare": ["compare", "difference", "contrast", "versus"],
            "list": ["list", "enumerate", "items", "examples"],
            "analyze": ["analyze", "analysis", "examine", "study"],
            "summarize": ["summarize", "summary", "overview", "brief"]
        }

        # Add related terms to query
        query_lower = query.lower()
        expanded_terms = []

        for key, synonyms in expansion_map.items():
            if key in query_lower:
                expanded_terms.extend([s for s in synonyms if s not in query_lower])

        if expanded_terms:
            # Add expanded terms as context
            expanded_query = f"{query}\n\nRelated terms: {', '.join(expanded_terms[:5])}"
            return expanded_query

    except Exception as e:
        st.warning(f"⚠️ Query expansion failed: {str(e)}")

    return query

# Main application
st.title("📚 Enhanced RAG Chatbot with LlamaIndex & Chroma DB")
st.markdown("Upload documents and ask questions with improved context retrieval!")

# Sidebar for configuration and document info
with st.sidebar:
    st.header("📊 System Status")

    # Initialize embeddings and show status
    if "embeddings_initialized" not in st.session_state:
        with st.spinner("🔄 Initializing embeddings..."):
            embedding_status = initialize_embeddings()
            st.info(embedding_status)
            st.session_state.embeddings_initialized = True

    # Display RAG configuration
    st.subheader("⚙️ Advanced RAG Configuration")

    # Show accuracy features
    accuracy_features = []
    if RAG_CONFIG.get("enable_reranking", False):
        accuracy_features.append("🎯 LLM Reranking")
    if RAG_CONFIG.get("enable_hybrid_search", False):
        accuracy_features.append("🔍 Hybrid Search")
    if RAG_CONFIG.get("enable_query_expansion", False):
        accuracy_features.append("📈 Query Expansion")
    if RAG_CONFIG.get("enable_metadata_filtering", False):
        accuracy_features.append("📊 Metadata Filtering")
    if RAG_CONFIG.get("sentence_splitter", False):
        accuracy_features.append("🎯 Semantic Splitting")

    if accuracy_features:
        st.write("**Active Accuracy Features:**")
        for feature in accuracy_features:
            st.write(f"✅ {feature}")

    # Show key parameters
    st.write("**Key Parameters:**")
    st.write(f"📏 Chunk Size: {RAG_CONFIG['chunk_size']}")
    st.write(f"🔄 Overlap: {RAG_CONFIG['chunk_overlap']}")
    st.write(f"🎯 Top-K: {RAG_CONFIG['similarity_top_k']}")
    st.write(f"📊 Cutoff: {RAG_CONFIG['similarity_cutoff']}")
    st.write(f"🧠 Context Window: {RAG_CONFIG['context_window']}")

    with st.expander("📋 Full Configuration"):
        st.json(RAG_CONFIG)

    # Document metadata display
    if os.path.exists(METADATA_FILE):
        metadata = load_document_metadata()
        if metadata:
            st.subheader("📄 Document Library")
            for filename, info in metadata.items():
                with st.expander(f"📄 {filename}"):
                    st.write(f"**Size:** {info['file_size']:,} bytes")
                    st.write(f"**Type:** {info['file_type']}")
                    st.write(f"**Modified:** {info['modified_time'][:19]}")
                    st.write(f"**Hash:** {info['file_hash'][:8]}...")

    # Conversation context info
    if RAG_CONFIG.get("enable_chat_memory", False):
        st.subheader("🧠 Conversation Memory")
        context_window = RAG_CONFIG.get("context_window", 3)
        st.write(f"**Context Window:** {context_window} message pairs")

        if st.session_state.get("messages", []):
            recent_count = min(len(st.session_state.messages), context_window * 2)
            st.write(f"**Active Context:** {recent_count} recent messages")
        else:
            st.write("**Active Context:** No messages yet")

    # Clear chat history button
    if st.button("🗑️ Clear Chat History"):
        st.session_state.messages = []
        st.rerun()

# File uploader
uploaded_files = st.file_uploader("Upload documents", accept_multiple_files=True, type=["pdf", "txt"])

if uploaded_files:
    # Save uploaded files
    for file in uploaded_files:
        file_path = os.path.join(DATA_DIR, file.name)
        with open(file_path, "wb") as f:
            f.write(file.getbuffer())

    st.success(f"✅ {len(uploaded_files)} files uploaded successfully!")

    # Update the vector index with new documents
    with st.spinner("🔄 Updating vector index..."):
        update_index()
        st.session_state.index_updated = True

# Initialize or load chat history
if "messages" not in st.session_state:
    st.session_state.messages = []
    
# Display chat history
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Query input
query = st.chat_input("Ask a question about your documents...")

if query:
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": query})
    with st.chat_message("user"):
        st.markdown(query)
    
    # Process query with LlamaIndex
    try:
        # Check if OpenAI API key is available
        if not os.getenv("OPENAI_API_KEY"):
            st.error("❌ OpenAI API key not found. Please add OPENAI_API_KEY to your .env file.")
            st.stop()

        # Load or create persistent index
        with st.spinner("🔍 Loading vector index..."):
            index = load_or_create_index()

        if index is None:
            st.error("❌ No documents available. Please upload some documents first.")
            st.stop()

        # Create conversational engine with memory
        engine = create_conversational_engine(index, st.session_state.messages)

        # Enhance query with conversational context
        enhanced_query = enhance_query_with_context(query, st.session_state.messages)

        with st.spinner("🤔 Analyzing documents and generating response..."):
            response = engine.query(enhanced_query)

        # Debug: Check if response is empty
        if not response or not response.response or response.response.strip() == "":
            st.error("❌ Received empty response. This might be due to:")
            st.write("- Similarity cutoff too high")
            st.write("- No relevant content found")
            st.write("- API key issues")
            st.write("- Document processing problems")

            # Try with a simpler query engine
            st.info("🔄 Trying with basic query engine...")
            simple_engine = index.as_query_engine(
                similarity_top_k=10,
                response_mode="compact"
            )
            response = simple_engine.query(query)

        # Display assistant response with enhanced context
        with st.chat_message("assistant"):
            st.markdown(response.response)

            # Show source context if available
            if hasattr(response, 'source_nodes') and response.source_nodes:
                with st.expander("📚 Source Context & References"):
                    for i, node in enumerate(response.source_nodes):
                        st.markdown(f"**Source {i+1}:**")

                        # Display metadata
                        if hasattr(node, 'metadata') and node.metadata:
                            col1, col2 = st.columns(2)
                            with col1:
                                if 'filename' in node.metadata:
                                    st.write(f"📄 **File:** {node.metadata['filename']}")
                                if 'file_type' in node.metadata:
                                    st.write(f"📋 **Type:** {node.metadata['file_type']}")
                            with col2:
                                if hasattr(node, 'score'):
                                    st.write(f"🎯 **Similarity:** {node.score:.3f}")
                                if 'file_size' in node.metadata:
                                    st.write(f"📏 **Size:** {node.metadata['file_size']:,} bytes")

                        # Display content excerpt
                        if hasattr(node, 'text'):
                            st.markdown("**Content:**")
                            st.text_area(
                                f"Context {i+1}",
                                node.text[:500] + "..." if len(node.text) > 500 else node.text,
                                height=100,
                                key=f"context_{i}"
                            )
                        st.divider()

        # Add to session state
        st.session_state.messages.append({"role": "assistant", "content": response.response})
        
        # Save to chat history file
        chat_history = load_chat_history()
        chat_history.append({
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "question": query,
            "answer": response.response
        })
        save_chat_history(chat_history)
        
    except Exception as e:
        st.error(f"Error processing query: {str(e)}")
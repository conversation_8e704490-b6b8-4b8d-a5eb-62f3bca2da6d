import os
from dotenv import load_dotenv
import streamlit as st
from llama_index.core import VectorStoreIndex, SimpleDirectoryReader, <PERSON>tings, StorageContext, Document
from llama_index.core.node_parser import SentenceSplitter
from llama_index.core.retrievers import VectorIndexRetriever
from llama_index.core.query_engine import RetrieverQueryEngine
from llama_index.core.postprocessor import SimilarityPostprocessor, MetadataReplacementPostProcessor
from llama_index.core.response_synthesizers import get_response_synthesizer
from llama_index.core.memory import ChatMemoryBuffer
from llama_index.core.chat_engine import CondensePlusContextChatEngine
from llama_index.llms.openai import OpenAI
from llama_index.vector_stores.chroma import ChromaVectorStore
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
import chromadb
import json
from datetime import datetime
import hashlib

# Load environment variables
load_dotenv()

# Setup directories
DATA_DIR = "documents"
CHROMA_DB_DIR = "chroma_db"
CHAT_HISTORY_FILE = "chat_history.json"
METADATA_FILE = "document_metadata.json"
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(CHROMA_DB_DIR, exist_ok=True)

# Enhanced RAG Configuration for Higher Accuracy
RAG_CONFIG = {
    "chunk_size": 256,          # Smaller chunks for better precision
    "chunk_overlap": 64,        # Higher overlap for better context continuity
    "similarity_top_k": 8,      # More candidates for better recall
    "similarity_cutoff": 0.2,   # Lower cutoff for better recall
    "response_mode": "tree_summarize",  # Better for complex queries
    "streaming": False,
    "rerank_top_n": 5,         # Re-rank top results for accuracy
    "context_window": 3,        # Number of previous messages to consider
    "enable_reranking": True,   # Enable semantic reranking
    "enable_chat_memory": True  # Enable conversational memory
}

# Initialize LLM with enhanced conversational settings
llm = OpenAI(
    model="gpt-4",
    temperature=0.1,
    max_tokens=1500,
    system_prompt="""You are a helpful AI assistant that answers questions based on the provided context and conversation history.

Key Instructions:
1. Use the provided document context to answer questions accurately
2. Remember and reference previous parts of our conversation when relevant
3. If the current question relates to previous questions, acknowledge that connection
4. Always cite source documents when possible with specific filenames
5. If you cannot find relevant information in the context, clearly state that
6. Maintain conversation flow by referencing earlier topics when appropriate
7. Be specific about what information comes from which document
8. If asked about something mentioned earlier, refer back to that context

Format your responses clearly and maintain conversational continuity."""
)
Settings.llm = llm

# Configure node parser for better chunking
Settings.node_parser = SentenceSplitter(
    chunk_size=RAG_CONFIG["chunk_size"],
    chunk_overlap=RAG_CONFIG["chunk_overlap"]
)

# Function to initialize embeddings
def initialize_embeddings():
    """Initialize embeddings with fallback to OpenAI"""
    try:
        embed_model = HuggingFaceEmbedding(model_name="BAAI/bge-small-en-v1.5")
        Settings.embed_model = embed_model
        return "🤖 Using local BGE embeddings"
    except Exception as e:
        # Will use OpenAI embeddings by default
        return f"⚠️ Using OpenAI embeddings (local model failed: {str(e)[:100]}...)"

# Load and save chat history
def load_chat_history():
    if os.path.exists(CHAT_HISTORY_FILE):
        with open(CHAT_HISTORY_FILE, "r") as f:
            return json.load(f)
    return []

def save_chat_history(history):
    with open(CHAT_HISTORY_FILE, "w") as f:
        json.dump(history, f, indent=4)

# Enhanced document metadata management
def load_document_metadata():
    """Load document metadata for tracking changes"""
    if os.path.exists(METADATA_FILE):
        with open(METADATA_FILE, "r") as f:
            return json.load(f)
    return {}

def save_document_metadata(metadata):
    """Save document metadata"""
    with open(METADATA_FILE, "w") as f:
        json.dump(metadata, f, indent=4)

def get_file_hash(file_path):
    """Get file hash for change detection"""
    with open(file_path, "rb") as f:
        return hashlib.md5(f.read()).hexdigest()

def process_documents_with_metadata():
    """Process documents with enhanced metadata"""
    documents = []
    metadata = load_document_metadata()

    for filename in os.listdir(DATA_DIR):
        file_path = os.path.join(DATA_DIR, filename)
        if os.path.isfile(file_path):
            file_hash = get_file_hash(file_path)
            file_stats = os.stat(file_path)

            # Create enhanced metadata
            doc_metadata = {
                "filename": filename,
                "file_path": file_path,
                "file_size": file_stats.st_size,
                "modified_time": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                "file_hash": file_hash,
                "file_type": filename.split('.')[-1].lower() if '.' in filename else 'unknown'
            }

            # Update metadata tracking
            metadata[filename] = doc_metadata

            # Load document with metadata
            try:
                reader = SimpleDirectoryReader(input_files=[file_path])
                file_docs = reader.load_data()

                # Enhance each document with metadata
                for doc in file_docs:
                    doc.metadata.update(doc_metadata)
                    # Add chunk information
                    doc.metadata["source_type"] = "document"
                    doc.metadata["processing_timestamp"] = datetime.now().isoformat()

                documents.extend(file_docs)

            except Exception as e:
                st.warning(f"⚠️ Could not process {filename}: {str(e)}")

    # Save updated metadata
    save_document_metadata(metadata)
    return documents

# Chroma DB and Index management functions
def get_chroma_client():
    """Initialize and return Chroma client"""
    return chromadb.PersistentClient(path=CHROMA_DB_DIR)

def load_or_create_index():
    """Load existing index from Chroma DB or create new one if it doesn't exist"""
    try:
        # Initialize Chroma client
        chroma_client = get_chroma_client()

        # Try to get existing collection
        try:
            chroma_collection = chroma_client.get_collection("documents")
            st.info("📂 Found existing Chroma collection")
        except:
            # Create new collection if it doesn't exist
            chroma_collection = chroma_client.create_collection("documents")
            st.info("🆕 Created new Chroma collection")

        # Create vector store
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
        storage_context = StorageContext.from_defaults(vector_store=vector_store)

        # Check if we have documents to index
        if not os.listdir(DATA_DIR):
            st.warning("📁 No documents found. Please upload some documents first.")
            return None

        # Load documents with enhanced metadata
        documents = process_documents_with_metadata()

        if not documents:
            st.warning("📁 No valid documents found to process.")
            return None

        # Create or load index
        if chroma_collection.count() == 0:
            # Create new index if collection is empty
            index = VectorStoreIndex.from_documents(
                documents, storage_context=storage_context
            )
            st.success("🔄 Created new vector index with Chroma DB")
        else:
            # Load existing index
            index = VectorStoreIndex.from_vector_store(
                vector_store, storage_context=storage_context
            )
            st.info("� Loaded existing vector index from Chroma DB")

        return index

    except Exception as e:
        st.error(f"Error with Chroma DB: {str(e)}")
        return None

def update_index():
    """Update the index when new documents are added"""
    try:
        # Initialize Chroma client
        chroma_client = get_chroma_client()

        # Delete existing collection and recreate (for simplicity)
        try:
            chroma_client.delete_collection("documents")
        except:
            pass

        # Create new collection
        chroma_collection = chroma_client.create_collection("documents")
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
        storage_context = StorageContext.from_defaults(vector_store=vector_store)

        # Load all documents (including new ones)
        documents = SimpleDirectoryReader(DATA_DIR).load_data()

        # Create new index with all documents
        index = VectorStoreIndex.from_documents(
            documents, storage_context=storage_context
        )

        st.success("🔄 Updated vector index with new documents in Chroma DB")
        return index

    except Exception as e:
        st.error(f"Error updating index: {str(e)}")
        return load_or_create_index()

def create_enhanced_query_engine(index):
    """Create query engine with enhanced context retrieval and accuracy"""
    try:
        # Enhanced retriever with more candidates
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=RAG_CONFIG["similarity_top_k"]
        )

        # Enhanced post-processors for better accuracy
        postprocessors = [
            SimilarityPostprocessor(similarity_cutoff=RAG_CONFIG["similarity_cutoff"])
        ]

        # Add reranking if enabled (for higher accuracy)
        if RAG_CONFIG.get("enable_reranking", False):
            try:
                from llama_index.core.postprocessor import LLMRerank
                postprocessors.append(
                    LLMRerank(
                        choice_batch_size=RAG_CONFIG["rerank_top_n"],
                        top_n=RAG_CONFIG["rerank_top_n"]
                    )
                )
                st.info("🎯 Using LLM reranking for higher accuracy")
            except Exception as e:
                st.warning(f"⚠️ Could not enable reranking: {str(e)}")

        response_synthesizer = get_response_synthesizer(
            response_mode=RAG_CONFIG["response_mode"],
            streaming=RAG_CONFIG["streaming"]
        )

        query_engine = RetrieverQueryEngine(
            retriever=retriever,
            response_synthesizer=response_synthesizer,
            node_postprocessors=postprocessors
        )

        st.info("🔧 Using enhanced query engine with improved accuracy")
        return query_engine

    except Exception as e:
        st.warning(f"⚠️ Enhanced query engine failed: {str(e)}")
        st.info("🔄 Falling back to simple query engine")
        # Fallback to simple but reliable query engine
        return index.as_query_engine(
            similarity_top_k=RAG_CONFIG["similarity_top_k"],
            response_mode=RAG_CONFIG["response_mode"]
        )

def create_conversational_engine(index, chat_history):
    """Create enhanced query engine with conversational context"""
    try:
        if not RAG_CONFIG.get("enable_chat_memory", False):
            return create_enhanced_query_engine(index)

        # Get recent conversation context
        context_window = RAG_CONFIG.get("context_window", 3)
        recent_messages = chat_history[-context_window*2:] if len(chat_history) > 0 else []

        # Create enhanced query engine
        query_engine = create_enhanced_query_engine(index)

        # Add conversation context to the query engine
        if recent_messages:
            st.info(f"🧠 Using conversational context ({len(recent_messages)} recent messages)")
        else:
            st.info("🔧 Using enhanced query engine (no conversation history)")

        return query_engine

    except Exception as e:
        st.warning(f"⚠️ Conversational engine failed: {str(e)}")
        st.info("🔄 Falling back to enhanced query engine")
        return create_enhanced_query_engine(index)

def enhance_query_with_context(query, chat_history):
    """Enhance the query with conversational context"""
    if not RAG_CONFIG.get("enable_chat_memory", False) or not chat_history:
        return query

    context_window = RAG_CONFIG.get("context_window", 3)
    recent_messages = chat_history[-context_window*2:] if len(chat_history) > 0 else []

    if not recent_messages:
        return query

    # Build context from recent conversation
    context_parts = []
    for msg in recent_messages[-6:]:  # Last 3 pairs
        if msg["role"] == "user":
            context_parts.append(f"Previous question: {msg['content']}")
        elif msg["role"] == "assistant":
            context_parts.append(f"Previous answer: {msg['content'][:200]}...")

    if context_parts:
        enhanced_query = f"""
Previous conversation context:
{chr(10).join(context_parts)}

Current question: {query}

Please answer the current question while considering the conversation context above. If the current question relates to previous topics, acknowledge that connection.
"""
        return enhanced_query

    return query

# Main application
st.title("📚 Enhanced RAG Chatbot with LlamaIndex & Chroma DB")
st.markdown("Upload documents and ask questions with improved context retrieval!")

# Sidebar for configuration and document info
with st.sidebar:
    st.header("📊 System Status")

    # Initialize embeddings and show status
    if "embeddings_initialized" not in st.session_state:
        with st.spinner("🔄 Initializing embeddings..."):
            embedding_status = initialize_embeddings()
            st.info(embedding_status)
            st.session_state.embeddings_initialized = True

    # Display RAG configuration
    st.subheader("⚙️ RAG Configuration")
    st.json(RAG_CONFIG)

    # Document metadata display
    if os.path.exists(METADATA_FILE):
        metadata = load_document_metadata()
        if metadata:
            st.subheader("📄 Document Library")
            for filename, info in metadata.items():
                with st.expander(f"📄 {filename}"):
                    st.write(f"**Size:** {info['file_size']:,} bytes")
                    st.write(f"**Type:** {info['file_type']}")
                    st.write(f"**Modified:** {info['modified_time'][:19]}")
                    st.write(f"**Hash:** {info['file_hash'][:8]}...")

    # Conversation context info
    if RAG_CONFIG.get("enable_chat_memory", False):
        st.subheader("🧠 Conversation Memory")
        context_window = RAG_CONFIG.get("context_window", 3)
        st.write(f"**Context Window:** {context_window} message pairs")

        if st.session_state.get("messages", []):
            recent_count = min(len(st.session_state.messages), context_window * 2)
            st.write(f"**Active Context:** {recent_count} recent messages")
        else:
            st.write("**Active Context:** No messages yet")

    # Clear chat history button
    if st.button("🗑️ Clear Chat History"):
        st.session_state.messages = []
        st.rerun()

# File uploader
uploaded_files = st.file_uploader("Upload documents", accept_multiple_files=True, type=["pdf", "txt"])

if uploaded_files:
    # Save uploaded files
    for file in uploaded_files:
        file_path = os.path.join(DATA_DIR, file.name)
        with open(file_path, "wb") as f:
            f.write(file.getbuffer())

    st.success(f"✅ {len(uploaded_files)} files uploaded successfully!")

    # Update the vector index with new documents
    with st.spinner("🔄 Updating vector index..."):
        update_index()
        st.session_state.index_updated = True

# Initialize or load chat history
if "messages" not in st.session_state:
    st.session_state.messages = []
    
# Display chat history
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Query input
query = st.chat_input("Ask a question about your documents...")

if query:
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": query})
    with st.chat_message("user"):
        st.markdown(query)
    
    # Process query with LlamaIndex
    try:
        # Check if OpenAI API key is available
        if not os.getenv("OPENAI_API_KEY"):
            st.error("❌ OpenAI API key not found. Please add OPENAI_API_KEY to your .env file.")
            st.stop()

        # Load or create persistent index
        with st.spinner("🔍 Loading vector index..."):
            index = load_or_create_index()

        if index is None:
            st.error("❌ No documents available. Please upload some documents first.")
            st.stop()

        # Create conversational engine with memory
        engine = create_conversational_engine(index, st.session_state.messages)

        # Enhance query with conversational context
        enhanced_query = enhance_query_with_context(query, st.session_state.messages)

        with st.spinner("🤔 Analyzing documents and generating response..."):
            response = engine.query(enhanced_query)

        # Debug: Check if response is empty
        if not response or not response.response or response.response.strip() == "":
            st.error("❌ Received empty response. This might be due to:")
            st.write("- Similarity cutoff too high")
            st.write("- No relevant content found")
            st.write("- API key issues")
            st.write("- Document processing problems")

            # Try with a simpler query engine
            st.info("🔄 Trying with basic query engine...")
            simple_engine = index.as_query_engine(
                similarity_top_k=10,
                response_mode="compact"
            )
            response = simple_engine.query(query)

        # Display assistant response with enhanced context
        with st.chat_message("assistant"):
            st.markdown(response.response)

            # Show source context if available
            if hasattr(response, 'source_nodes') and response.source_nodes:
                with st.expander("📚 Source Context & References"):
                    for i, node in enumerate(response.source_nodes):
                        st.markdown(f"**Source {i+1}:**")

                        # Display metadata
                        if hasattr(node, 'metadata') and node.metadata:
                            col1, col2 = st.columns(2)
                            with col1:
                                if 'filename' in node.metadata:
                                    st.write(f"📄 **File:** {node.metadata['filename']}")
                                if 'file_type' in node.metadata:
                                    st.write(f"📋 **Type:** {node.metadata['file_type']}")
                            with col2:
                                if hasattr(node, 'score'):
                                    st.write(f"🎯 **Similarity:** {node.score:.3f}")
                                if 'file_size' in node.metadata:
                                    st.write(f"📏 **Size:** {node.metadata['file_size']:,} bytes")

                        # Display content excerpt
                        if hasattr(node, 'text'):
                            st.markdown("**Content:**")
                            st.text_area(
                                f"Context {i+1}",
                                node.text[:500] + "..." if len(node.text) > 500 else node.text,
                                height=100,
                                key=f"context_{i}"
                            )
                        st.divider()

        # Add to session state
        st.session_state.messages.append({"role": "assistant", "content": response.response})
        
        # Save to chat history file
        chat_history = load_chat_history()
        chat_history.append({
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "question": query,
            "answer": response.response
        })
        save_chat_history(chat_history)
        
    except Exception as e:
        st.error(f"Error processing query: {str(e)}")
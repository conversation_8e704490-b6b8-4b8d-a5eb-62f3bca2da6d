import os
from dotenv import load_dotenv
import streamlit as st
from llama_index.core import VectorStoreIndex, SimpleDirectoryReader, Settings
from llama_index.llms.openai import OpenAI
import json
from datetime import datetime

# Load environment variables
load_dotenv()

# Setup directories
DATA_DIR = "documents"
CHAT_HISTORY_FILE = "chat_history.json"
os.makedirs(DATA_DIR, exist_ok=True)

# Initialize OpenAI
llm = OpenAI(model="gpt-4")
Settings.llm = llm

# Load and save chat history
def load_chat_history():
    if os.path.exists(CHAT_HISTORY_FILE):
        with open(CHAT_HISTORY_FILE, "r") as f:
            return json.load(f)
    return []

def save_chat_history(history):
    with open(CHAT_HISTORY_FILE, "w") as f:
        json.dump(history, f, indent=4)

# Main application
st.title("📚 RAG Chatbot with LlamaIndex")

# File uploader
uploaded_files = st.file_uploader("Upload documents", accept_multiple_files=True, type=["pdf", "txt"])

if uploaded_files:
    # Save uploaded files
    for file in uploaded_files:
        file_path = os.path.join(DATA_DIR, file.name)
        with open(file_path, "wb") as f:
            f.write(file.getbuffer())
    
    st.success(f"✅ {len(uploaded_files)} files uploaded successfully!")

# Initialize or load chat history
if "messages" not in st.session_state:
    st.session_state.messages = []
    
# Display chat history
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Query input
query = st.chat_input("Ask a question about your documents...")

if query:
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": query})
    with st.chat_message("user"):
        st.markdown(query)
    
    # Process query with LlamaIndex
    try:
        # Load documents
        documents = SimpleDirectoryReader(DATA_DIR).load_data()
        
        # Create index
        index = VectorStoreIndex.from_documents(documents)
        
        # Query engine
        query_engine = index.as_query_engine()
        response = query_engine.query(query)
        
        # Display assistant response
        with st.chat_message("assistant"):
            st.markdown(response.response)
        
        # Add to session state
        st.session_state.messages.append({"role": "assistant", "content": response.response})
        
        # Save to chat history file
        chat_history = load_chat_history()
        chat_history.append({
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "question": query,
            "answer": response.response
        })
        save_chat_history(chat_history)
        
    except Exception as e:
        st.error(f"Error processing query: {str(e)}")
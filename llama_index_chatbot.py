import os
import sys
import warnings
from dotenv import load_dotenv

# Fix PyTorch compatibility issues with Python 3.13
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=FutureWarning)

# Set environment variables to avoid PyTorch issues
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["HF_HUB_DISABLE_SYMLINKS_WARNING"] = "1"

import streamlit as st

try:
    from llama_index.core import VectorStoreIndex, TreeIndex, SimpleDirectoryReader, Settings, StorageContext, Document
    from llama_index.core.node_parser import SentenceSplitter
    from llama_index.core.retrievers import VectorIndexRetriever
    from llama_index.core.query_engine import RetrieverQueryEngine, SubQuestionQueryEngine
    from llama_index.core.postprocessor import SimilarityPostprocessor, MetadataReplacementPostProcessor, KeywordNodePostprocessor
    from llama_index.core.response_synthesizers import get_response_synthesizer
    from llama_index.core.memory import Chat<PERSON><PERSON>ory<PERSON>uffer
    from llama_index.core.chat_engine import CondensePlusContextChatEngine
    from llama_index.core.tools import QueryEngineTool, ToolMetadata
    from llama_index.llms.google_genai import GoogleGenAI
    from llama_index.vector_stores.chroma import ChromaVectorStore
    import chromadb

    # Try to import advanced features, fallback if they fail
    try:
        from llama_index.core.node_parser import SemanticSplitterNodeParser
        SEMANTIC_SPLITTER_AVAILABLE = True
    except ImportError:
        SEMANTIC_SPLITTER_AVAILABLE = False

    try:
        from llama_index.core.extractors import TitleExtractor, QuestionsAnsweredExtractor, SummaryExtractor
        from llama_index.core.schema import MetadataMode
        EXTRACTORS_AVAILABLE = True
    except ImportError:
        EXTRACTORS_AVAILABLE = False

    try:
        from llama_index.embeddings.huggingface import HuggingFaceEmbedding
        HUGGINGFACE_AVAILABLE = True
    except ImportError:
        HUGGINGFACE_AVAILABLE = False

except ImportError as e:
    st.error(f"❌ Failed to import required libraries: {str(e)}")
    st.stop()

import json
from datetime import datetime
import hashlib
import re
from collections import defaultdict

# Load environment variables
load_dotenv()

# Prevent OpenAI fallback by setting environment variable
os.environ.setdefault("OPENAI_API_KEY", "")  # Set empty to prevent fallback

# Import configuration and setup modules
from config import RAG_CONFIG, TECHNICAL_PATTERNS, SUPPORTED_FILE_TYPES
from llm_setup import setup_llm_and_embeddings, get_llm_status, get_embedding_status
from accuracy_features import (
    extract_keywords_from_query, perform_precision_search,
    expand_query_for_better_matching, get_accuracy_features
)
from project_manager import render_project_selector, ProjectManager

# Initialize embeddings FIRST to prevent OpenAI fallback
from llama_index.embeddings.huggingface import HuggingFaceEmbedding
from llama_index.core import Settings

# Set embeddings immediately to prevent OpenAI fallback
embedding_initialized = False

# Block OpenAI completely first
os.environ["OPENAI_API_KEY"] = "sk-dummy_key_to_prevent_openai_fallback"

# Try to use a simple, reliable embedding approach
try:
    # Use LlamaIndex's built-in local embedding without HuggingFace complications
    from llama_index.core.embeddings import resolve_embed_model
    Settings.embed_model = resolve_embed_model("local:BAAI/bge-small-en-v1.5")
    print("✅ Local BGE embeddings initialized successfully")
    embedding_initialized = True
except Exception as e:
    print(f"⚠️ Local BGE embeddings failed: {e}")

    try:
        # Try with a different local model
        Settings.embed_model = resolve_embed_model("local:sentence-transformers/all-MiniLM-L6-v2")
        print("✅ Local MiniLM embeddings initialized successfully")
        embedding_initialized = True
    except Exception as e2:
        print(f"⚠️ Local MiniLM embeddings failed: {e2}")

        try:
            # Use the simplest possible local embedding
            Settings.embed_model = resolve_embed_model("local")
            print("✅ Default local embeddings initialized successfully")
            embedding_initialized = True
        except Exception as e3:
            print(f"⚠️ All local embeddings failed: {e3}")
            print("🚫 Using LlamaIndex default embeddings (OpenAI blocked)")
            # LlamaIndex will use its default, but OpenAI is blocked

# Initialize LLM from separate module
llm_setup_result = setup_llm_and_embeddings()

# Project-specific path helper
def get_project_paths():
    """Get paths for current project"""
    if "current_project_id" not in st.session_state:
        return None

    pm = ProjectManager()
    return pm.get_project_paths(st.session_state.current_project_id)

# Configure advanced node parser for maximum accuracy
def create_advanced_node_parser():
    """Create advanced node parser with semantic splitting"""
    try:
        # Always use enhanced sentence splitter for reliability
        # Semantic splitter requires embeddings to be initialized first
        sentence_parser = SentenceSplitter(
            chunk_size=RAG_CONFIG["chunk_size"],
            chunk_overlap=RAG_CONFIG["chunk_overlap"],
            paragraph_separator="\n\n",
            secondary_chunking_regex="[^,.;。？！]+[,.;。？！]?",
            tokenizer=None  # Use default tokenizer
        )
        return sentence_parser

    except Exception as e:
        st.warning(f"⚠️ Advanced parser failed: {str(e)}")
        # Ultimate fallback
        return SentenceSplitter(
            chunk_size=RAG_CONFIG["chunk_size"],
            chunk_overlap=RAG_CONFIG["chunk_overlap"]
        )

def create_semantic_parser_after_embeddings():
    """Create semantic parser after embeddings are initialized"""
    try:
        if RAG_CONFIG.get("sentence_splitter", True) and SEMANTIC_SPLITTER_AVAILABLE:
            # Only try semantic splitter after embeddings are ready
            if hasattr(Settings, 'embed_model') and Settings.embed_model is not None:
                try:
                    semantic_parser = SemanticSplitterNodeParser(
                        buffer_size=1,
                        breakpoint_percentile_threshold=95,
                        embed_model=Settings.embed_model
                    )
                    st.info("🎯 Using semantic splitter for enhanced accuracy")
                    return semantic_parser
                except Exception as e:
                    st.warning(f"⚠️ Semantic splitter failed: {str(e)}")

        # Return None to use the default sentence splitter
        return None
    except Exception as e:
        st.warning(f"⚠️ Semantic parser creation failed: {str(e)}")
        return None

# Set the advanced node parser
Settings.node_parser = create_advanced_node_parser()

# Function to initialize embeddings
def initialize_embeddings():
    """Initialize embeddings with fallback to default"""
    if HUGGINGFACE_AVAILABLE:
        try:
            # Use the recommended HuggingFace embedding setup
            Settings.embed_model = HuggingFaceEmbedding(
                model_name="BAAI/bge-small-en-v1.5"
            )
            return "🤖 Using local BGE embeddings (BAAI/bge-small-en-v1.5)"
        except Exception as e:
            return f"⚠️ Using default embeddings (local model failed: {str(e)[:100]}...)"
    else:
        return "⚠️ Using default embeddings (HuggingFace not available)"

# Load and save chat history
def load_chat_history():
    paths = get_project_paths()
    if not paths:
        return []

    if os.path.exists(paths["chat_history_file"]):
        with open(paths["chat_history_file"], "r") as f:
            return json.load(f)
    return []

def save_chat_history(history):
    paths = get_project_paths()
    if not paths:
        return

    with open(paths["chat_history_file"], "w") as f:
        json.dump(history, f, indent=4)

# Enhanced document metadata management
def load_document_metadata():
    """Load document metadata for tracking changes"""
    paths = get_project_paths()
    if not paths:
        return {}

    if os.path.exists(paths["metadata_file"]):
        with open(paths["metadata_file"], "r") as f:
            return json.load(f)
    return {}

def save_document_metadata(metadata):
    """Save document metadata"""
    paths = get_project_paths()
    if not paths:
        return

    with open(paths["metadata_file"], "w") as f:
        json.dump(metadata, f, indent=4)

def get_file_hash(file_path):
    """Get file hash for change detection"""
    with open(file_path, "rb") as f:
        return hashlib.md5(f.read()).hexdigest()

def process_documents_with_advanced_metadata():
    """Process documents with advanced metadata extraction and quality control"""
    documents = []
    metadata = load_document_metadata()

    # Focus on core accuracy improvements: better chunking, reranking, and hybrid search
    # This provides the main accuracy gains without complex metadata extraction
    st.info("📝 Using optimized basic metadata for maximum reliability")

    paths = get_project_paths()
    if not paths:
        return

    for filename in os.listdir(paths["documents_dir"]):
        file_path = os.path.join(paths["documents_dir"], filename)
        if os.path.isfile(file_path):
            file_hash = get_file_hash(file_path)
            file_stats = os.stat(file_path)

            # Create concise metadata to avoid ChromaDB length issues
            doc_metadata = {
                "filename": filename,
                "file_size": file_stats.st_size,
                "file_hash": file_hash[:16],  # Shortened hash
                "file_type": filename.split('.')[-1].lower() if '.' in filename else 'unknown',
                "source": "doc"  # Shortened
            }

            # Update metadata tracking
            metadata[filename] = doc_metadata

            # Load document with metadata
            try:
                reader = SimpleDirectoryReader(input_files=[file_path])
                file_docs = reader.load_data()

                # Process each document
                for doc in file_docs:
                    # Add basic metadata
                    doc.metadata.update(doc_metadata)

                    # Quality filtering - skip very short documents
                    if len(doc.text.strip()) < RAG_CONFIG.get("min_chunk_size", 50):
                        st.warning(f"⚠️ Skipping {filename}: too short ({len(doc.text)} chars)")
                        continue

                    # Add enhanced basic metadata for better indexing
                    try:
                        # Extract basic content features for better indexing
                        text_length = len(doc.text)
                        word_count = len(doc.text.split())

                        # Add minimal content-based metadata
                        doc.metadata.update({
                            "words": word_count,
                            "lang": "en"
                        })

                        # Extract short excerpt
                        first_words = " ".join(doc.text.split()[:5])
                        if first_words:
                            doc.metadata["excerpt"] = first_words[:50] + "..."

                    except Exception as e:
                        st.warning(f"⚠️ Basic metadata extraction failed for {filename}: {str(e)}")

                    # Add simple quality score (shortened key)
                    doc.metadata["quality"] = round(calculate_document_quality(doc.text), 2)

                    documents.append(doc)

            except Exception as e:
                st.warning(f"⚠️ Could not process {filename}: {str(e)}")

    # Filter documents by quality
    quality_threshold = RAG_CONFIG.get("quality_threshold", 0.1)
    high_quality_docs = [
        doc for doc in documents
        if doc.metadata.get("quality", 0) >= quality_threshold
    ]

    if len(high_quality_docs) < len(documents):
        st.info(f"📊 Quality filtering: {len(high_quality_docs)}/{len(documents)} documents passed quality check")

    # Save updated metadata
    save_document_metadata(metadata)

    # Create keyword index for precise searching
    if RAG_CONFIG.get("enable_keyword_index", False) and high_quality_docs:
        try:
            keyword_index = create_keyword_index(high_quality_docs)
            # Store in session state for precise keyword searches
            st.session_state.keyword_index = keyword_index
            st.info(f"🔍 Created keyword index for {len(keyword_index)} documents")
        except Exception as e:
            st.warning(f"⚠️ Keyword index creation failed: {str(e)}")

    return high_quality_docs

def calculate_document_quality(text):
    """Calculate a quality score for document content"""
    if not text or len(text.strip()) < 10:
        return 0.0

    # Basic quality metrics
    word_count = len(text.split())
    sentence_count = len([s for s in text.split('.') if s.strip()])
    avg_word_length = sum(len(word) for word in text.split()) / max(word_count, 1)

    # Quality indicators
    has_punctuation = any(p in text for p in '.!?')
    has_uppercase = any(c.isupper() for c in text)
    has_lowercase = any(c.islower() for c in text)

    # Calculate score (0-1)
    quality_score = 0.0

    # Word count factor (optimal around 50-500 words per chunk)
    if 50 <= word_count <= 500:
        quality_score += 0.3
    elif word_count > 20:
        quality_score += 0.1

    # Sentence structure
    if sentence_count > 1:
        quality_score += 0.2

    # Average word length (2-8 chars is good)
    if 2 <= avg_word_length <= 8:
        quality_score += 0.2

    # Basic formatting
    if has_punctuation:
        quality_score += 0.1
    if has_uppercase and has_lowercase:
        quality_score += 0.1

    # Avoid very repetitive text
    unique_words = len(set(text.lower().split()))
    if unique_words / max(word_count, 1) > 0.3:
        quality_score += 0.1

    return min(quality_score, 1.0)

def create_keyword_index(documents):
    """Create a precise keyword index for exact matching"""
    keyword_index = defaultdict(list)

    for doc_idx, doc in enumerate(documents):
        filename = doc.metadata.get('filename', f'document_{doc_idx}')
        text = doc.text

        # Split text into sentences/paragraphs for precise location tracking
        sentences = re.split(r'[.!?]+', text)

        for sent_idx, sentence in enumerate(sentences):
            if sentence.strip():
                # Store sentence with location metadata
                keyword_index[filename].append({
                    'sentence_idx': sent_idx,
                    'text': sentence.strip(),
                    'start_pos': text.find(sentence.strip()),
                    'length': len(sentence.strip())
                })

    return keyword_index

# Precision search functions moved to accuracy_features.py

def find_all_occurrences(text, keyword):
    """Find all occurrences of keyword in text"""
    matches = []
    start = 0
    while True:
        pos = text.find(keyword, start)
        if pos == -1:
            break
        matches.append(pos)
        start = pos + 1
    return matches

def find_fuzzy_matches(text, keyword, threshold=0.8):
    """Find fuzzy matches using simple similarity"""
    matches = []
    words = text.split()

    for i, word in enumerate(words):
        # Simple similarity check
        similarity = calculate_similarity(word.lower(), keyword.lower())
        if similarity >= threshold:
            pos = text.find(word)
            matches.append({
                'position': pos,
                'confidence': similarity,
                'matched_text': word
            })

    return matches

def calculate_similarity(str1, str2):
    """Calculate simple string similarity"""
    if str1 == str2:
        return 1.0

    # Simple character-based similarity
    longer = str1 if len(str1) > len(str2) else str2
    shorter = str2 if len(str1) > len(str2) else str1

    if len(longer) == 0:
        return 1.0

    # Count matching characters
    matches = sum(1 for a, b in zip(longer, shorter) if a == b)
    return matches / len(longer)

def generate_case_variants(keyword):
    """Generate case variants of a keyword"""
    variants = [
        keyword.upper(),
        keyword.lower(),
        keyword.title(),
        keyword.capitalize()
    ]
    return list(set(variants))

def extract_page_section_info(text, keyword_positions):
    """Extract page numbers and section information around keyword matches"""
    page_section_info = []

    for pos_info in keyword_positions:
        context = pos_info['context']

        # Look for page references in the context
        page_patterns = [
            r'page\s+(\d+)',
            r'p\.\s*(\d+)',
            r'pg\s+(\d+)',
            r'\bpage\s*(\d+)',
        ]

        section_patterns = [
            r'section\s+(\d+(?:\.\d+)*)',
            r'§\s*(\d+(?:\.\d+)*)',
            r'chapter\s+(\d+)',
            r'part\s+(\d+)',
        ]

        pages = []
        sections = []

        # Search for page numbers
        for pattern in page_patterns:
            matches = re.finditer(pattern, context, re.IGNORECASE)
            for match in matches:
                pages.append(match.group(1))

        # Search for section numbers
        for pattern in section_patterns:
            matches = re.finditer(pattern, context, re.IGNORECASE)
            for match in matches:
                sections.append(match.group(1))

        page_section_info.append({
            'filename': pos_info['filename'],
            'keyword': pos_info['keyword'],
            'context': context,
            'pages': pages,
            'sections': sections,
            'sentence_idx': pos_info['sentence_idx']
        })

    return page_section_info

# Chroma DB and Index management functions
def get_chroma_client():
    """Initialize and return Chroma client"""
    paths = get_project_paths()
    if not paths:
        return None
    return chromadb.PersistentClient(path=paths["chroma_db_dir"])

def create_hybrid_indices():
    """Create both Vector and Tree indices for comprehensive document understanding"""
    try:
        # Check if we have documents to index
        paths = get_project_paths()
        if not paths or not os.listdir(paths["documents_dir"]):
            st.warning("📁 No documents found. Please upload some documents first.")
            return None, None

        # Load documents with enhanced metadata
        documents = process_documents_with_advanced_metadata()

        if not documents:
            st.warning("📁 No valid documents found to process.")
            return None, None

        vector_index = None
        tree_index = None

        # Create Vector Index for semantic similarity search
        try:
            chroma_client = get_chroma_client()
            try:
                chroma_collection = chroma_client.get_collection("documents")
                st.info("📂 Found existing Chroma collection")
            except:
                chroma_collection = chroma_client.create_collection("documents")
                st.info("🆕 Created new Chroma collection")

            vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
            storage_context = StorageContext.from_defaults(vector_store=vector_store)

            if chroma_collection.count() == 0:
                vector_index = VectorStoreIndex.from_documents(
                    documents, storage_context=storage_context
                )
                st.success("🔄 Created new vector index with Chroma DB")
            else:
                vector_index = VectorStoreIndex.from_vector_store(
                    vector_store, storage_context=storage_context
                )
                st.info("📚 Loaded existing vector index from Chroma DB")
        except Exception as e:
            st.warning(f"⚠️ Vector index creation failed: {str(e)}")

        # Create Tree Index for hierarchical document understanding
        try:
            tree_index = TreeIndex.from_documents(documents)
            st.success("🌳 Created tree index for comprehensive document analysis")
        except Exception as e:
            st.warning(f"⚠️ Tree index creation failed: {str(e)}")

        return vector_index, tree_index

    except Exception as e:
        st.error(f"Error creating hybrid indices: {str(e)}")
        return None, None

def load_or_create_index():
    """Load existing index from Chroma DB or create new one if it doesn't exist"""
    try:
        # Initialize Chroma client
        chroma_client = get_chroma_client()

        # Try to get existing collection
        try:
            chroma_collection = chroma_client.get_collection("documents")
            st.info("📂 Found existing Chroma collection")
        except:
            # Create new collection if it doesn't exist
            chroma_collection = chroma_client.create_collection("documents")
            st.info("🆕 Created new Chroma collection")

        # Create vector store
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
        storage_context = StorageContext.from_defaults(vector_store=vector_store)

        # Check if we have documents to index
        paths = get_project_paths()
        if not paths or not os.listdir(paths["documents_dir"]):
            st.warning("📁 No documents found. Please upload some documents first.")
            return None

        # Load documents with enhanced metadata
        documents = process_documents_with_advanced_metadata()

        if not documents:
            st.warning("📁 No valid documents found to process.")
            return None

        # Create or load index
        if chroma_collection.count() == 0:
            # Create new index if collection is empty
            index = VectorStoreIndex.from_documents(
                documents, storage_context=storage_context
            )
            st.success("🔄 Created new vector index with Chroma DB")
        else:
            # Load existing index
            index = VectorStoreIndex.from_vector_store(
                vector_store, storage_context=storage_context
            )
            st.info("� Loaded existing vector index from Chroma DB")

        return index

    except Exception as e:
        st.error(f"Error with Chroma DB: {str(e)}")
        return None

def update_index():
    """Update the index when new documents are added"""
    try:
        # Initialize Chroma client
        chroma_client = get_chroma_client()

        # Delete existing collection and recreate (for simplicity)
        try:
            chroma_client.delete_collection("documents")
        except:
            pass

        # Create new collection
        chroma_collection = chroma_client.create_collection("documents")
        vector_store = ChromaVectorStore(chroma_collection=chroma_collection)
        storage_context = StorageContext.from_defaults(vector_store=vector_store)

        # Load all documents (including new ones)
        paths = get_project_paths()
        if not paths:
            return None
        documents = SimpleDirectoryReader(paths["documents_dir"]).load_data()

        # Create new index with all documents
        index = VectorStoreIndex.from_documents(
            documents, storage_context=storage_context
        )

        st.success("🔄 Updated vector index with new documents in Chroma DB")
        return index

    except Exception as e:
        st.error(f"Error updating index: {str(e)}")
        return load_or_create_index()

def create_enhanced_query_engine(index):
    """Create query engine with maximum indexing accuracy and hybrid search"""
    try:
        # Enhanced retriever with more candidates for better recall
        retriever = VectorIndexRetriever(
            index=index,
            similarity_top_k=RAG_CONFIG["similarity_top_k"]
        )

        # Advanced post-processors for maximum accuracy
        postprocessors = []

        # 1. Similarity filtering
        postprocessors.append(
            SimilarityPostprocessor(similarity_cutoff=RAG_CONFIG["similarity_cutoff"])
        )

        # 2. Keyword-based filtering for hybrid search
        if RAG_CONFIG.get("enable_hybrid_search", False):
            try:
                postprocessors.append(
                    KeywordNodePostprocessor(
                        required_keywords=[],  # Will be set dynamically
                        exclude_keywords=["advertisement", "footer", "header"]
                    )
                )
                st.info("🔍 Hybrid search enabled (semantic + keyword)")
            except Exception as e:
                st.warning(f"⚠️ Keyword filtering failed: {str(e)}")

        # 3. Metadata filtering for better relevance
        if RAG_CONFIG.get("enable_metadata_filtering", False):
            try:
                postprocessors.append(
                    MetadataReplacementPostProcessor(target_metadata_key="window")
                )
                st.info("📊 Metadata filtering enabled")
            except Exception as e:
                st.warning(f"⚠️ Metadata filtering failed: {str(e)}")

        # 4. LLM reranking for highest accuracy
        if RAG_CONFIG.get("enable_reranking", False):
            try:
                from llama_index.core.postprocessor import LLMRerank
                postprocessors.append(
                    LLMRerank(
                        choice_batch_size=RAG_CONFIG["rerank_top_n"],
                        top_n=RAG_CONFIG["rerank_top_n"],
                        llm=Settings.llm
                    )
                )
                st.info("🎯 LLM reranking enabled for maximum accuracy")
            except Exception as e:
                st.warning(f"⚠️ LLM reranking failed: {str(e)}")

        # Enhanced response synthesizer
        response_synthesizer = get_response_synthesizer(
            response_mode=RAG_CONFIG["response_mode"],
            streaming=RAG_CONFIG["streaming"],
            use_async=False
        )

        # Create the enhanced query engine
        query_engine = RetrieverQueryEngine(
            retriever=retriever,
            response_synthesizer=response_synthesizer,
            node_postprocessors=postprocessors
        )

        st.success(f"� Enhanced query engine created with {len(postprocessors)} accuracy layers")
        return query_engine

    except Exception as e:
        st.warning(f"⚠️ Enhanced query engine failed: {str(e)}")
        st.info("🔄 Falling back to simple query engine")
        # Fallback to simple but reliable query engine
        return index.as_query_engine(
            similarity_top_k=RAG_CONFIG["similarity_top_k"],
            response_mode=RAG_CONFIG["response_mode"]
        )

def create_hybrid_query_engine(vector_index, tree_index=None):
    """Create hybrid query engine using both Vector and Tree indices for better answers"""
    try:
        # Create tools for different query types
        query_engines = []

        # Vector search for semantic similarity
        if vector_index:
            vector_engine = create_enhanced_query_engine(vector_index)
            query_engines.append(
                QueryEngineTool(
                    query_engine=vector_engine,
                    metadata=ToolMetadata(
                        name="vector_search",
                        description="Use this for semantic similarity search and specific fact retrieval"
                    )
                )
            )

        # Tree search for comprehensive analysis
        if tree_index:
            tree_engine = tree_index.as_query_engine(
                response_mode="tree_summarize",
                use_async=False
            )
            query_engines.append(
                QueryEngineTool(
                    query_engine=tree_engine,
                    metadata=ToolMetadata(
                        name="tree_search",
                        description="Use this for comprehensive document analysis and complex questions"
                    )
                )
            )
            st.success("🌳 Hybrid engine: Vector + Tree indices for comprehensive answers")
        else:
            st.info("🔍 Using enhanced vector search only")

        # Create sub-question query engine for complex queries
        if len(query_engines) > 1:
            hybrid_engine = SubQuestionQueryEngine.from_defaults(
                query_engine_tools=query_engines,
                llm=Settings.llm,
                use_async=False
            )
            return hybrid_engine
        elif len(query_engines) == 1:
            return query_engines[0].query_engine
        else:
            return vector_index.as_query_engine()

    except Exception as e:
        st.warning(f"⚠️ Hybrid engine creation failed: {str(e)}")
        return create_enhanced_query_engine(vector_index)

def create_conversational_engine(index, chat_history):
    """Create enhanced query engine with conversational context"""
    try:
        if not RAG_CONFIG.get("enable_chat_memory", False):
            # Check if we have tree index for hybrid approach
            tree_index = st.session_state.get("tree_index", None)
            return create_hybrid_query_engine(index, tree_index)

        # Get recent conversation context
        context_window = RAG_CONFIG.get("context_window", 3)
        recent_messages = chat_history[-context_window*2:] if len(chat_history) > 0 else []

        # Create hybrid query engine
        tree_index = st.session_state.get("tree_index", None)
        query_engine = create_hybrid_query_engine(index, tree_index)

        # Add conversation context to the query engine
        if recent_messages:
            st.info(f"🧠 Using conversational context ({len(recent_messages)} recent messages)")
        else:
            st.info("🔧 Using hybrid query engine (no conversation history)")

        return query_engine

    except Exception as e:
        st.warning(f"⚠️ Conversational engine failed: {str(e)}")
        st.info("🔄 Falling back to enhanced query engine")
        return create_enhanced_query_engine(index)

def perform_precision_search(query, keyword_index=None):
    """Perform high-precision search combining semantic and exact keyword matching"""
    precision_results = {
        'exact_matches': [],
        'keyword_count': 0,
        'locations': [],
        'semantic_results': None
    }

    if not keyword_index:
        return precision_results

    # Extract potential keywords from query
    keywords_to_search = extract_keywords_from_query(query)

    if keywords_to_search:
        # Perform exact keyword search
        from accuracy_features import precise_keyword_search
        exact_matches = precise_keyword_search(keyword_index, keywords_to_search)

        # Group by keyword and count occurrences
        keyword_counts = defaultdict(int)
        keyword_locations = defaultdict(list)

        for match in exact_matches:
            keyword = match['keyword']
            keyword_counts[keyword] += 1
            keyword_locations[keyword].append({
                'filename': match['filename'],
                'sentence_idx': match['sentence_idx'],
                'context': match['context']
            })

        precision_results['exact_matches'] = exact_matches
        precision_results['keyword_count'] = sum(keyword_counts.values())
        precision_results['keyword_breakdown'] = dict(keyword_counts)
        precision_results['locations'] = dict(keyword_locations)

        st.info(f"🎯 Precision search found {precision_results['keyword_count']} exact matches")

    return precision_results

def extract_keywords_from_query(query):
    """Extract specific keywords/phrases from query for exact matching"""
    keywords = []

    # Look for quoted phrases (exact matches)
    quoted_phrases = re.findall(r'"([^"]*)"', query)
    keywords.extend(quoted_phrases)

    # Look for technical references (like AASHTO T 27)
    technical_patterns = [
        r'\b[A-Z]{2,}\s+[A-Z]?\s*\d+\b',  # AASHTO T 27, ASTM D 123, etc.
        r'\b[A-Z]{2,}-\d+\b',             # ISO-123, ANSI-456, etc.
        r'\bSection\s+\d+(?:\.\d+)*\b',   # Section 3.2.1
        r'\bChapter\s+\d+\b',             # Chapter 5
        r'\bTable\s+\d+(?:\.\d+)*\b',     # Table 3.1
        r'\bFigure\s+\d+(?:\.\d+)*\b',    # Figure 2.3
    ]

    for pattern in technical_patterns:
        matches = re.findall(pattern, query, re.IGNORECASE)
        keywords.extend(matches)

    # Remove duplicates and empty strings
    keywords = list(set([k.strip() for k in keywords if k.strip()]))

    return keywords

def manage_chat_context(chat_history):
    """Advanced chat context management with compression and warnings"""
    if not chat_history:
        return chat_history, {"status": "empty", "message": "No conversation history"}

    max_messages = RAG_CONFIG.get("max_context_messages", 50)
    warning_threshold = RAG_CONFIG.get("context_warning_threshold", 40)
    token_limit = RAG_CONFIG.get("context_token_limit", 8000)

    # Calculate approximate token count
    total_tokens = sum(len(msg["content"].split()) * 1.3 for msg in chat_history)  # Rough estimate

    context_info = {
        "total_messages": len(chat_history),
        "estimated_tokens": int(total_tokens),
        "max_messages": max_messages,
        "token_limit": token_limit,
        "status": "normal"
    }

    # Check if we need to compress or truncate
    if len(chat_history) > max_messages:
        # Keep recent messages and compress older ones
        if RAG_CONFIG.get("enable_context_compression", False):
            compressed_history = compress_old_context(chat_history, max_messages)
            context_info["status"] = "compressed"
            context_info["message"] = f"Compressed {len(chat_history) - len(compressed_history)} older messages"
            return compressed_history, context_info
        else:
            # Simple truncation
            truncated_history = chat_history[-max_messages:]
            context_info["status"] = "truncated"
            context_info["message"] = f"Truncated {len(chat_history) - len(truncated_history)} older messages"
            return truncated_history, context_info

    elif len(chat_history) > warning_threshold:
        context_info["status"] = "warning"
        context_info["message"] = f"Approaching context limit ({len(chat_history)}/{max_messages} messages)"

    elif total_tokens > token_limit * 0.8:
        context_info["status"] = "token_warning"
        context_info["message"] = f"High token usage ({int(total_tokens)}/{token_limit} tokens)"

    return chat_history, context_info

def compress_old_context(chat_history, keep_recent=20):
    """Compress older conversation context while preserving recent messages"""
    if len(chat_history) <= keep_recent:
        return chat_history

    # Keep recent messages
    recent_messages = chat_history[-keep_recent:]
    old_messages = chat_history[:-keep_recent]

    # Create summary of old context
    if old_messages:
        old_summary = create_context_summary(old_messages)
        summary_message = {
            "role": "system",
            "content": f"[COMPRESSED CONTEXT] Previous conversation summary: {old_summary}"
        }
        return [summary_message] + recent_messages

    return recent_messages

def create_context_summary(messages):
    """Create a summary of conversation messages"""
    topics = []
    questions = []

    for msg in messages:
        if msg["role"] == "user":
            questions.append(msg["content"][:100])
        elif msg["role"] == "assistant":
            # Extract key topics from assistant responses
            content = msg["content"][:200]
            topics.append(content)

    summary_parts = []
    if questions:
        summary_parts.append(f"Previous questions: {'; '.join(questions[-3:])}")
    if topics:
        summary_parts.append(f"Previous topics: {'; '.join(topics[-3:])}")

    return " | ".join(summary_parts)

# Caching System Functions
def load_cache(cache_file):
    """Load cache from file"""
    try:
        if os.path.exists(cache_file):
            with open(cache_file, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        st.warning(f"⚠️ Could not load cache: {str(e)}")
    return {}

def save_cache(cache_file, cache_data):
    """Save cache to file"""
    try:
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        st.warning(f"⚠️ Could not save cache: {str(e)}")

def get_cache_key(query, document_hashes):
    """Generate cache key for query and document state"""
    query_hash = hashlib.md5(query.encode()).hexdigest()
    doc_hash = hashlib.md5(str(sorted(document_hashes)).encode()).hexdigest()
    return f"{query_hash}_{doc_hash}"

def is_cache_valid(cache_entry, ttl_hours=24):
    """Check if cache entry is still valid"""
    if not cache_entry or 'timestamp' not in cache_entry:
        return False

    cache_time = datetime.fromisoformat(cache_entry['timestamp'])
    current_time = datetime.now()
    age_hours = (current_time - cache_time).total_seconds() / 3600

    return age_hours < ttl_hours

def get_cached_response(query, document_hashes):
    """Get cached response if available and valid"""
    if not RAG_CONFIG.get("enable_response_caching", False):
        return None

    paths = get_project_paths()
    if not paths:
        return None

    cache = load_cache(paths["response_cache_file"])
    cache_key = get_cache_key(query, document_hashes)

    if cache_key in cache:
        cache_entry = cache[cache_key]
        if is_cache_valid(cache_entry, RAG_CONFIG.get("cache_ttl_hours", 24)):
            st.info("🚀 Using cached response")
            return cache_entry

    return None

def cache_response(query, document_hashes, response, precision_results=None):
    """Cache response for future use"""
    if not RAG_CONFIG.get("enable_response_caching", False):
        return

    paths = get_project_paths()
    if not paths:
        return

    cache = load_cache(paths["response_cache_file"])
    cache_key = get_cache_key(query, document_hashes)

    cache_entry = {
        'query': query,
        'response': response.response if hasattr(response, 'response') else str(response),
        'precision_results': precision_results,
        'timestamp': datetime.now().isoformat(),
        'source_nodes': []
    }

    # Store source nodes if available
    if hasattr(response, 'source_nodes') and response.source_nodes:
        cache_entry['source_nodes'] = [
            {
                'text': node.text[:500],
                'metadata': node.metadata,
                'score': getattr(node, 'score', None)
            }
            for node in response.source_nodes[:5]
        ]

    cache[cache_key] = cache_entry
    save_cache(paths["response_cache_file"], cache)

# User Feedback System
def load_feedback_data():
    """Load user feedback data"""
    try:
        paths = get_project_paths()
        if not paths:
            return []

        if os.path.exists(paths["feedback_file"]):
            with open(paths["feedback_file"], 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        st.warning(f"⚠️ Could not load feedback data: {str(e)}")
    return []

def save_feedback_data(feedback_data):
    """Save user feedback data"""
    try:
        paths = get_project_paths()
        if not paths:
            return

        with open(paths["feedback_file"], 'w', encoding='utf-8') as f:
            json.dump(feedback_data, f, indent=2, ensure_ascii=False)
    except Exception as e:
        st.warning(f"⚠️ Could not save feedback data: {str(e)}")

def record_user_feedback(query, response, rating, feedback_text=""):
    """Record user feedback for learning"""
    if not RAG_CONFIG.get("enable_user_feedback", False):
        return

    feedback_data = load_feedback_data()

    feedback_entry = {
        'timestamp': datetime.now().isoformat(),
        'query': query,
        'response': response[:500] if len(response) > 500 else response,
        'rating': rating,
        'feedback_text': feedback_text,
        'session_id': st.session_state.get('session_id', 'unknown')
    }

    feedback_data.append(feedback_entry)
    save_feedback_data(feedback_data)

    # Update learning data
    if RAG_CONFIG.get("enable_adaptive_learning", False):
        update_learning_data(query, rating, feedback_text)

def update_learning_data(query, rating, feedback_text):
    """Update learning data based on user feedback"""
    try:
        paths = get_project_paths()
        if not paths:
            return

        learning_data = load_cache(paths["learning_data_file"])

        # Extract keywords from query
        keywords = extract_keywords_from_query(query)

        # Update keyword performance scores
        for keyword in keywords:
            if keyword not in learning_data:
                learning_data[keyword] = {
                    'total_queries': 0,
                    'total_rating': 0,
                    'avg_rating': 0,
                    'feedback_count': 0
                }

            learning_data[keyword]['total_queries'] += 1
            learning_data[keyword]['total_rating'] += rating
            learning_data[keyword]['avg_rating'] = learning_data[keyword]['total_rating'] / learning_data[keyword]['total_queries']

            if feedback_text:
                learning_data[keyword]['feedback_count'] += 1

        save_cache(paths["learning_data_file"], learning_data)

    except Exception as e:
        st.warning(f"⚠️ Could not update learning data: {str(e)}")

def get_learning_insights():
    """Get insights from learning data"""
    try:
        paths = get_project_paths()
        if not paths:
            return None

        learning_data = load_cache(paths["learning_data_file"])

        if not learning_data:
            return None

        # Calculate overall statistics
        total_keywords = len(learning_data)
        avg_ratings = [data['avg_rating'] for data in learning_data.values()]
        overall_avg = sum(avg_ratings) / len(avg_ratings) if avg_ratings else 0

        # Find best and worst performing keywords
        best_keywords = sorted(learning_data.items(), key=lambda x: x[1]['avg_rating'], reverse=True)[:3]
        worst_keywords = sorted(learning_data.items(), key=lambda x: x[1]['avg_rating'])[:3]

        return {
            'total_keywords': total_keywords,
            'overall_avg_rating': overall_avg,
            'best_keywords': best_keywords,
            'worst_keywords': worst_keywords
        }

    except Exception as e:
        st.warning(f"⚠️ Could not get learning insights: {str(e)}")
        return None

def enhance_query_with_context(query, chat_history):
    """Enhance the query with advanced conversational context"""
    if not RAG_CONFIG.get("enable_chat_memory", False) or not chat_history:
        return expand_query_for_better_matching(query)

    # Manage context with compression/truncation
    managed_history, context_info = manage_chat_context(chat_history)

    # Store context info for sidebar display
    st.session_state.context_info = context_info

    context_window = RAG_CONFIG.get("context_window", 8)
    recent_messages = managed_history[-context_window*2:] if len(managed_history) > 0 else []

    if not recent_messages:
        return expand_query_for_better_matching(query)

    # Build enhanced context from recent conversation
    context_parts = []
    for msg in recent_messages:
        if msg["role"] == "user":
            context_parts.append(f"Previous question: {msg['content']}")
        elif msg["role"] == "assistant":
            # Include more context but truncate if too long
            content = msg['content'][:300] + "..." if len(msg['content']) > 300 else msg['content']
            context_parts.append(f"Previous answer: {content}")
        elif msg["role"] == "system":
            context_parts.append(f"Context: {msg['content']}")

    if context_parts:
        enhanced_query = f"""
Previous conversation context:
{chr(10).join(context_parts)}

Current question: {query}

Please answer the current question while considering the conversation context above. If the current question relates to previous topics, acknowledge that connection and build upon previous discussions.
"""
        return expand_query_for_better_matching(enhanced_query)

    return expand_query_for_better_matching(query)

def expand_query_for_better_matching(query):
    """Expand query with synonyms and related terms for better retrieval accuracy"""
    if not RAG_CONFIG.get("enable_query_expansion", False):
        return query

    try:
        # Simple query expansion with common synonyms and variations
        expansion_map = {
            "what": ["what", "which", "describe", "explain"],
            "how": ["how", "method", "process", "way", "approach"],
            "why": ["why", "reason", "cause", "purpose", "rationale"],
            "when": ["when", "time", "date", "period", "timing"],
            "where": ["where", "location", "place", "position"],
            "who": ["who", "person", "people", "individual", "author"],
            "define": ["define", "definition", "meaning", "explanation"],
            "compare": ["compare", "difference", "contrast", "versus"],
            "list": ["list", "enumerate", "items", "examples"],
            "analyze": ["analyze", "analysis", "examine", "study"],
            "summarize": ["summarize", "summary", "overview", "brief"]
        }

        # Add related terms to query
        query_lower = query.lower()
        expanded_terms = []

        for key, synonyms in expansion_map.items():
            if key in query_lower:
                expanded_terms.extend([s for s in synonyms if s not in query_lower])

        if expanded_terms:
            # Add expanded terms as context
            expanded_query = f"{query}\n\nRelated terms: {', '.join(expanded_terms[:5])}"
            return expanded_query

    except Exception as e:
        st.warning(f"⚠️ Query expansion failed: {str(e)}")

    return query

# Main application
st.title("🤖 Gemini 2.0 Flash Ultra-Precision RAG Chatbot")
st.markdown("⚡ **Lightning-fast document analysis** with **advanced caching**, **user feedback learning**, and **ultra-precision keyword search**!")

# Project Management in Sidebar
project_id, pm = render_project_selector()

# Check if project is selected
if not project_id:
    st.warning("📁 Please select or create a project to continue.")
    st.info("👈 Use the sidebar to create your first project!")
    st.stop()

# Set current project in session state
st.session_state.current_project_id = project_id

# Sidebar for configuration and document info
with st.sidebar:
    st.header("📊 System Status")

    # Display LLM status
    st.write(f"**LLM:** {get_llm_status()}")

    # Initialize embeddings and show status
    if "embeddings_initialized" not in st.session_state:
        with st.spinner("🔄 Initializing embeddings..."):
            embedding_status = initialize_embeddings()
            st.info(embedding_status)
            st.session_state.embeddings_initialized = True

            # Try to upgrade to semantic splitter after embeddings are ready
            semantic_parser = create_semantic_parser_after_embeddings()
            if semantic_parser is not None:
                Settings.node_parser = semantic_parser

    # Display RAG configuration
    st.subheader("⚙️ Advanced RAG Configuration")

    # Show accuracy features in a compact expandable section
    accuracy_features = get_accuracy_features()

    if accuracy_features:
        with st.expander(f"🎯 Active Accuracy Features ({len(accuracy_features)})"):
            for feature in accuracy_features:
                st.write(f"✅ {feature}")
    else:
        st.info("⚠️ No accuracy features enabled")

    # Show key parameters
    st.write("**Key Parameters:**")
    st.write(f"📏 Chunk Size: {RAG_CONFIG['chunk_size']}")
    st.write(f"🔄 Overlap: {RAG_CONFIG['chunk_overlap']}")
    st.write(f"🎯 Top-K: {RAG_CONFIG['similarity_top_k']}")
    st.write(f"📊 Cutoff: {RAG_CONFIG['similarity_cutoff']}")
    st.write(f"🧠 Context Window: {RAG_CONFIG['context_window']}")

    with st.expander("📋 Full Configuration"):
        st.json(RAG_CONFIG)

    # Document metadata display
    paths = get_project_paths()
    if paths and os.path.exists(paths["metadata_file"]):
        metadata = load_document_metadata()
        if metadata:
            st.subheader("📄 Document Library")
            for filename, info in metadata.items():
                with st.expander(f"📄 {filename}"):
                    st.write(f"**Size:** {info['file_size']:,} bytes")
                    st.write(f"**Type:** {info['file_type']}")
                    st.write(f"**Modified:** {info['modified_time'][:19]}")
                    st.write(f"**Hash:** {info['file_hash'][:8]}...")

    # Enhanced Conversation context info
    if RAG_CONFIG.get("enable_chat_memory", False):
        st.subheader("🧠 Enhanced Conversation Memory")

        # Get context info from session state
        context_info = st.session_state.get("context_info", {})

        # Display context window settings
        context_window = RAG_CONFIG.get("context_window", 8)
        max_messages = RAG_CONFIG.get("max_context_messages", 50)
        st.write(f"**Context Window:** {context_window} message pairs")
        st.write(f"**Max Messages:** {max_messages}")

        # Display current context status
        if st.session_state.get("messages", []):
            total_messages = len(st.session_state.messages)
            recent_count = min(total_messages, context_window * 2)

            # Context status indicator
            status = context_info.get("status", "normal")
            if status == "normal":
                st.success(f"✅ **Active Context:** {recent_count}/{total_messages} messages")
            elif status == "warning":
                st.warning(f"⚠️ **Active Context:** {recent_count}/{total_messages} messages")
                st.write(f"📊 {context_info.get('message', '')}")
            elif status == "compressed":
                st.info(f"🗜️ **Compressed Context:** {recent_count} messages")
                st.write(f"📊 {context_info.get('message', '')}")
            elif status == "truncated":
                st.error(f"✂️ **Context Lost:** {recent_count}/{total_messages} messages")
                st.write(f"📊 {context_info.get('message', '')}")
            elif status == "token_warning":
                st.warning(f"🔤 **Token Usage High:** {recent_count} messages")
                st.write(f"📊 {context_info.get('message', '')}")

            # Token usage display
            if "estimated_tokens" in context_info:
                token_usage = context_info["estimated_tokens"]
                token_limit = context_info.get("token_limit", 8000)
                usage_percent = (token_usage / token_limit) * 100

                if usage_percent > 80:
                    st.error(f"🔤 **Token Usage:** {token_usage}/{token_limit} ({usage_percent:.1f}%)")
                elif usage_percent > 60:
                    st.warning(f"🔤 **Token Usage:** {token_usage}/{token_limit} ({usage_percent:.1f}%)")
                else:
                    st.info(f"🔤 **Token Usage:** {token_usage}/{token_limit} ({usage_percent:.1f}%)")
        else:
            st.write("**Active Context:** No messages yet")

    # Learning Insights Section
    if RAG_CONFIG.get("enable_adaptive_learning", False):
        st.subheader("🧠 Learning Insights")

        insights = get_learning_insights()
        if insights:
            st.write(f"**Keywords Analyzed:** {insights['total_keywords']}")
            st.write(f"**Overall Rating:** {insights['overall_avg_rating']:.2f}/5.0")

            # Show best performing keywords
            if insights['best_keywords']:
                with st.expander("🏆 Best Performing Keywords"):
                    for keyword, data in insights['best_keywords']:
                        st.write(f"**{keyword}:** {data['avg_rating']:.2f}/5.0 ({data['total_queries']} queries)")

            # Show worst performing keywords
            if insights['worst_keywords']:
                with st.expander("⚠️ Needs Improvement"):
                    for keyword, data in insights['worst_keywords']:
                        if data['avg_rating'] < 3.0:  # Only show if below average
                            st.write(f"**{keyword}:** {data['avg_rating']:.2f}/5.0 ({data['total_queries']} queries)")
        else:
            st.write("No learning data available yet")

    # Cache Statistics
    if RAG_CONFIG.get("enable_query_caching", False):
        st.subheader("🚀 Cache Performance")

        try:
            paths = get_project_paths()
            if paths:
                cache = load_cache(paths["response_cache_file"])
            else:
                cache = {}
            if cache:
                total_cached = len(cache)
                valid_cached = sum(1 for entry in cache.values() if is_cache_valid(entry))

                st.write(f"**Total Cached:** {total_cached}")
                st.write(f"**Valid Cache:** {valid_cached}")

                if total_cached > 0:
                    hit_rate = (valid_cached / total_cached) * 100
                    if hit_rate > 80:
                        st.success(f"**Cache Hit Rate:** {hit_rate:.1f}%")
                    elif hit_rate > 50:
                        st.warning(f"**Cache Hit Rate:** {hit_rate:.1f}%")
                    else:
                        st.error(f"**Cache Hit Rate:** {hit_rate:.1f}%")
            else:
                st.write("No cache data available")
        except Exception as e:
            st.write("Cache statistics unavailable")

    # Clear chat history button
    if st.button("🗑️ Clear Chat History"):
        st.session_state.messages = []
        st.rerun()

# File uploader
st.header("📁 Document Upload")
uploaded_files = st.file_uploader(
    "Upload your documents",
    accept_multiple_files=True,
    type=["pdf", "txt", "docx", "doc", "rtf", "odt", "html", "htm", "md", "csv", "xlsx", "xls"],
    help="Upload documents to analyze. Supported formats: PDF, TXT, DOCX, DOC, RTF, ODT, HTML, MD, CSV, XLSX, XLS"
)

if uploaded_files:
    paths = get_project_paths()
    if paths:
        # Save uploaded files to project directory
        for file in uploaded_files:
            file_path = os.path.join(paths["documents_dir"], file.name)
            with open(file_path, "wb") as f:
                f.write(file.getbuffer())

        st.success(f"✅ Uploaded {len(uploaded_files)} file(s) to project successfully!")

        # Update metadata for uploaded files
        # TODO: Implement update_document_metadata() with project paths
        # update_document_metadata()

        # Update project stats
        pm.update_project(project_id, document_count=len(uploaded_files))

        # Clear any existing indices to force rebuild
        if "vector_index" in st.session_state:
            del st.session_state.vector_index
        if "tree_index" in st.session_state:
            del st.session_state.tree_index
        if "keyword_index" in st.session_state:
            del st.session_state.keyword_index

        st.info("🔄 Indices will be rebuilt with new documents on next query.")
    else:
        st.error("❌ No project selected. Please select a project first.")

# Initialize or load chat history
if "messages" not in st.session_state:
    st.session_state.messages = []
    
# Display chat history
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Query input
query = st.chat_input("Ask a question about your documents...")

if query:
    # Add user message to chat history
    st.session_state.messages.append({"role": "user", "content": query})
    with st.chat_message("user"):
        st.markdown(query)
    
    # Process query with LlamaIndex
    try:
        # Check if Google API key is available
        if not os.getenv("GOOGLE_API_KEY"):
            st.error("❌ Google API key not found. Please add GOOGLE_API_KEY to your .env file.")
            st.stop()

        # Check for cached response first
        document_hashes = []
        paths = get_project_paths()
        if paths and os.path.exists(paths["metadata_file"]):
            metadata = load_document_metadata()
            document_hashes = [info.get('file_hash', '') for info in metadata.values()]

        cached_response = get_cached_response(query, document_hashes)
        if cached_response:
            # Use cached response
            response_text = cached_response['response']
            precision_results = cached_response.get('precision_results')

            # Display cached response
            with st.chat_message("assistant"):
                st.markdown(response_text)
                st.info("🚀 This response was retrieved from cache for faster performance")

                # Show cached precision results if available
                if precision_results and precision_results.get('keyword_count', 0) > 0:
                    with st.expander("🎯 Cached Precision Search Results"):
                        st.success(f"**Total Matches Found:** {precision_results['keyword_count']}")
                        for keyword, count in precision_results.get('keyword_breakdown', {}).items():
                            st.write(f"**'{keyword}':** {count} occurrences")

            # Add to session state for feedback
            st.session_state.messages.append({"role": "assistant", "content": response_text})
            st.session_state.last_query = query
            st.session_state.last_response = response_text
            st.session_state.last_precision_results = precision_results

        else:
            # Process query normally
            # Load or create persistent index
            with st.spinner("🔍 Loading vector index..."):
                index = load_or_create_index()

            if index is None:
                st.error("❌ No documents available. Please upload some documents first.")
                st.stop()

            # Perform precision keyword search first
            precision_results = None
            if RAG_CONFIG.get("enable_exact_keyword_search", False):
                keyword_index = st.session_state.get("keyword_index", None)
                if keyword_index:
                    with st.spinner("🔍 Performing precision keyword search..."):
                        precision_results = perform_precision_search(query, keyword_index)

        # Create conversational engine with memory
        engine = create_conversational_engine(index, st.session_state.messages)

        # Enhance query with conversational context and precision results
        enhanced_query = enhance_query_with_context(query, st.session_state.messages)

        # Add precision search context if available
        if precision_results and precision_results['keyword_count'] > 0:
            precision_context = f"""
PRECISION SEARCH RESULTS:
Found {precision_results['keyword_count']} exact keyword matches:
{precision_results['keyword_breakdown']}

Please use this exact count information along with the semantic search results to provide a comprehensive and accurate answer.
"""
            enhanced_query = precision_context + "\n\n" + enhanced_query

        with st.spinner("🤔 Analyzing documents and generating response..."):
            response = engine.query(enhanced_query)

        # Debug: Check if response is empty
        if not response or not response.response or response.response.strip() == "":
            st.error("❌ Received empty response. This might be due to:")
            st.write("- Similarity cutoff too high")
            st.write("- No relevant content found")
            st.write("- API key issues")
            st.write("- Document processing problems")

            # Try with a simpler query engine
            st.info("🔄 Trying with basic query engine...")
            simple_engine = index.as_query_engine(
                similarity_top_k=10,
                response_mode="compact"
            )
            response = simple_engine.query(query)

        # Display assistant response with enhanced context
        with st.chat_message("assistant"):
            st.markdown(response.response)

            # Show enhanced precision search results if available
            if precision_results and precision_results['keyword_count'] > 0:
                with st.expander("🎯 Ultra-Precision Search Results"):
                    st.success(f"**Total Matches Found:** {precision_results['keyword_count']}")

                    # Group results by match type
                    exact_matches = [m for m in precision_results['exact_matches'] if m.get('match_type') == 'exact']
                    fuzzy_matches = [m for m in precision_results['exact_matches'] if m.get('match_type') == 'fuzzy']
                    case_matches = [m for m in precision_results['exact_matches'] if m.get('match_type') == 'case_variant']

                    if exact_matches:
                        st.write(f"🎯 **Exact Matches:** {len(exact_matches)}")
                    if fuzzy_matches:
                        st.write(f"🔍 **Fuzzy Matches:** {len(fuzzy_matches)}")
                    if case_matches:
                        st.write(f"🔤 **Case Variants:** {len(case_matches)}")

                    for keyword, count in precision_results['keyword_breakdown'].items():
                        st.markdown(f"### **'{keyword}':** {count} occurrences")

                        # Show locations for this keyword
                        if keyword in precision_results['locations']:
                            locations = precision_results['locations'][keyword]
                            for i, location in enumerate(locations[:7]):  # Show more locations
                                # Get match details for this location
                                match_details = next((m for m in precision_results['exact_matches']
                                                    if m['filename'] == location['filename']
                                                    and m['sentence_idx'] == location['sentence_idx']), None)

                                match_type = match_details.get('match_type', 'exact') if match_details else 'exact'
                                confidence = match_details.get('confidence', 1.0) if match_details else 1.0

                                # Color code by match type
                                if match_type == 'exact':
                                    type_indicator = "🎯 Exact"
                                elif match_type == 'fuzzy':
                                    type_indicator = f"🔍 Fuzzy ({confidence:.2f})"
                                elif match_type == 'case_variant':
                                    type_indicator = "🔤 Case Variant"
                                else:
                                    type_indicator = "📍 Match"

                                st.text_area(
                                    f"{type_indicator} - Context {i+1} in {location['filename']}",
                                    location['context'][:400] + "..." if len(location['context']) > 400 else location['context'],
                                    height=100,
                                    key=f"precision_{keyword}_{i}"
                                )
                            if len(locations) > 7:
                                st.info(f"... and {len(locations) - 7} more occurrences")

                    # Summary statistics
                    st.markdown("### 📊 **Search Statistics:**")
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Total Matches", precision_results['keyword_count'])
                    with col2:
                        st.metric("Unique Keywords", len(precision_results['keyword_breakdown']))
                    with col3:
                        avg_confidence = sum(m.get('confidence', 1.0) for m in precision_results['exact_matches']) / len(precision_results['exact_matches'])
                        st.metric("Avg Confidence", f"{avg_confidence:.2f}")

            # Show source context if available
            if hasattr(response, 'source_nodes') and response.source_nodes:
                with st.expander("📚 Source Context & References"):
                    for i, node in enumerate(response.source_nodes):
                        st.markdown(f"**Source {i+1}:**")

                        # Display metadata
                        if hasattr(node, 'metadata') and node.metadata:
                            col1, col2 = st.columns(2)
                            with col1:
                                if 'filename' in node.metadata:
                                    st.write(f"📄 **File:** {node.metadata['filename']}")
                                if 'file_type' in node.metadata:
                                    st.write(f"📋 **Type:** {node.metadata['file_type']}")
                            with col2:
                                if hasattr(node, 'score'):
                                    st.write(f"🎯 **Similarity:** {node.score:.3f}")
                                if 'file_size' in node.metadata:
                                    st.write(f"📏 **Size:** {node.metadata['file_size']:,} bytes")

                        # Display content excerpt
                        if hasattr(node, 'text'):
                            st.markdown("**Content:**")
                            st.text_area(
                                f"Context {i+1}",
                                node.text[:500] + "..." if len(node.text) > 500 else node.text,
                                height=100,
                                key=f"context_{i}"
                            )
                        st.divider()

            # Cache the response for future use
            cache_response(query, document_hashes, response, precision_results)

            # Add to session state
            st.session_state.messages.append({"role": "assistant", "content": response.response})
            st.session_state.last_query = query
            st.session_state.last_response = response.response
            st.session_state.last_precision_results = precision_results

            # Save to chat history file
            chat_history = load_chat_history()
            chat_history.append({
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "question": query,
                "answer": response.response
            })
            save_chat_history(chat_history)

    except Exception as e:
        st.error(f"Error processing query: {str(e)}")

# User Feedback Section
if RAG_CONFIG.get("enable_user_feedback", False) and st.session_state.get("last_response"):
    st.markdown("---")
    st.subheader("📝 Rate This Response")

    # Initialize feedback submitted flag
    if "feedback_submitted" not in st.session_state:
        st.session_state.feedback_submitted = False

    # Reset feedback form if new response
    current_response_id = hash(st.session_state.get("last_response", ""))
    if st.session_state.get("last_response_id") != current_response_id:
        st.session_state.feedback_submitted = False
        st.session_state.last_response_id = current_response_id

    if not st.session_state.feedback_submitted:
        col1, col2 = st.columns([3, 1])

        with col1:
            # Rating system with unique key
            rating = st.select_slider(
                "How helpful was this response?",
                options=[1, 2, 3, 4, 5],
                value=3,
                format_func=lambda x: "⭐" * x,
                key=f"response_rating_{current_response_id}"
            )

            # Optional feedback text with unique key
            feedback_text = st.text_area(
                "Additional feedback (optional):",
                placeholder="Tell us how we can improve...",
                height=80,
                key=f"feedback_text_{current_response_id}"
            )

        with col2:
            st.write("")  # Spacing
            st.write("")  # Spacing
            if st.button("📤 Submit Feedback", type="primary", key=f"submit_feedback_{current_response_id}"):
                if st.session_state.get("last_query") and st.session_state.get("last_response"):
                    record_user_feedback(
                        st.session_state.last_query,
                        st.session_state.last_response,
                        rating,
                        feedback_text
                    )
                    st.session_state.feedback_submitted = True
                    st.success("✅ Thank you for your feedback!")
                    st.balloons()
                    st.rerun()
    else:
        st.success("✅ Feedback submitted for this response!")
        if st.button("📝 Rate Another Response", key=f"rate_another_{current_response_id}"):
            st.session_state.feedback_submitted = False
            st.rerun()
# LLM and Embeddings Setup
import os
import streamlit as st
from llama_index.core import Settings
from config import DEFAULT_SYSTEM_PROMPT

# Import availability flags
HUGGINGFACE_AVAILABLE = False
GOOGLE_GENAI_AVAILABLE = False

try:
    from llama_index.embeddings.huggingface import HuggingF<PERSON>Embedding
    HUGGINGFACE_AVAILABLE = True
except ImportError:
    pass

try:
    from llama_index.llms.google_genai import GoogleGenAI
    GOOGLE_GENAI_AVAILABLE = True
except ImportError:
    pass

def check_api_keys():
    """Check if required API keys are available"""
    if not os.getenv("GOOGLE_API_KEY"):
        st.error("❌ Google API key not found. Please add GOOGLE_API_KEY to your .env file.")
        st.info("Get your API key from: https://aistudio.google.com/")
        return False
    return True

def initialize_llm():
    """Initialize Google Gemini LLM"""
    if not <PERSON><PERSON><PERSON>LE_GENAI_AVAILABLE:
        st.error("❌ Google GenAI package not available. Please install: pip install llama-index-llms-google-genai")
        return None
    
    if not check_api_keys():
        return None
    
    try:
        # Try the correct Gemini model name
        llm = GoogleGenAI(
            model="gemini-2.0-flash-exp",  # Updated model name
            temperature=0.3,  # Slightly higher for more varied responses
            max_tokens=2000,  # Increased token limit
            system_prompt=DEFAULT_SYSTEM_PROMPT + "\n\nPlease provide detailed, comprehensive answers with explanations and context."
        )
        Settings.llm = llm
        st.success(f"✅ Google Gemini LLM initialized successfully")
        return llm
    except Exception as e:
        st.error(f"❌ Failed to initialize Google Gemini LLM: {str(e)}")
        # Try fallback model
        try:
            llm = GoogleGenAI(
                model="gemini-1.5-flash",  # Fallback model
                temperature=0.1,
                max_tokens=1500,
                system_prompt=DEFAULT_SYSTEM_PROMPT
            )
            Settings.llm = llm
            st.warning(f"⚠️ Using fallback Gemini model: gemini-1.5-flash")
            return llm
        except Exception as e2:
            st.error(f"❌ Fallback model also failed: {str(e2)}")
            return None

def initialize_embeddings():
    """Initialize embeddings with fallback to local (no OpenAI)"""
    # Check if embeddings are already set
    if hasattr(Settings, 'embed_model') and Settings.embed_model is not None:
        if hasattr(Settings.embed_model, '_model_name'):
            return f"🤖 Using pre-initialized embeddings ({Settings.embed_model._model_name})"
        else:
            return "🤖 Using pre-initialized simple embeddings"

    # Don't try to override if already set in main module
    return "🤖 Using embeddings from main initialization"

def get_llm_status():
    """Get current LLM status for display"""
    if GOOGLE_GENAI_AVAILABLE and os.getenv("GOOGLE_API_KEY"):
        return "🤖 Google Gemini 2.0 Flash (GenAI)"
    else:
        return "❌ Google Gemini not available"

def get_embedding_status():
    """Get current embedding status for display"""
    if HUGGINGFACE_AVAILABLE:
        return "🤖 Local BGE embeddings available"
    else:
        return "⚠️ Using default embeddings"

def setup_llm_and_embeddings():
    """Complete setup of LLM and embeddings"""
    # Initialize LLM
    llm = initialize_llm()
    if llm is None:
        st.stop()
    
    # Initialize embeddings
    embedding_status = initialize_embeddings()
    
    return {
        "llm": llm,
        "embedding_status": embedding_status,
        "llm_status": get_llm_status(),
        "embedding_available": HUGGINGFACE_AVAILABLE
    }

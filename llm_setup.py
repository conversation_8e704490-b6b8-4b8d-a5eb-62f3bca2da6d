# LLM and Embeddings Setup
import os
import streamlit as st
from llama_index.core import Settings
from config import DEFAULT_SYSTEM_PROMPT

# Import availability flags
HUGGINGFACE_AVAILABLE = False
GOOGLE_GENAI_AVAILABLE = False

try:
    from llama_index.embeddings.huggingface import Hugging<PERSON><PERSON>Embedding
    HUGGINGFACE_AVAILABLE = True
except ImportError:
    pass

try:
    from llama_index.llms.google_genai import GoogleGenAI
    GOOGLE_GENAI_AVAILABLE = True
except ImportError:
    pass

def check_api_keys():
    """Check if required API keys are available"""
    if not os.getenv("GOOGLE_API_KEY"):
        st.error("❌ Google API key not found. Please add GOOGLE_API_KEY to your .env file.")
        st.info("Get your API key from: https://aistudio.google.com/")
        return False
    return True

def initialize_llm():
    """Initialize Google Gemini LLM"""
    if not <PERSON><PERSON>G<PERSON>_GENAI_AVAILABLE:
        st.error("❌ Google GenAI package not available. Please install: pip install llama-index-llms-google-genai")
        return None
    
    if not check_api_keys():
        return None
    
    try:
        llm = GoogleGenAI(
            model="gemini-2.5-flash-preview-05-20",
            temperature=0.1,
            max_tokens=1500,
            system_prompt=DEFAULT_SYSTEM_PROMPT
        )
        Settings.llm = llm
        return llm
    except Exception as e:
        st.error(f"❌ Failed to initialize Google Gemini LLM: {str(e)}")
        return None

def initialize_embeddings():
    """Initialize embeddings with fallback to local (no OpenAI)"""
    # Check if embeddings are already set
    if hasattr(Settings, 'embed_model') and Settings.embed_model is not None:
        if hasattr(Settings.embed_model, 'model_name'):
            return f"🤖 Using pre-initialized embeddings ({Settings.embed_model.model_name})"
        else:
            return "🤖 Using pre-initialized embeddings"

    if HUGGINGFACE_AVAILABLE:
        try:
            # Use the recommended HuggingFace embedding setup with CPU device
            Settings.embed_model = HuggingFaceEmbedding(
                model_name="BAAI/bge-small-en-v1.5",
                device="cpu"
            )
            return "🤖 Using local BGE embeddings (BAAI/bge-small-en-v1.5) on CPU"
        except Exception as e:
            # Fallback to a smaller model instead of OpenAI
            try:
                Settings.embed_model = HuggingFaceEmbedding(
                    model_name="sentence-transformers/all-MiniLM-L6-v2",
                    device="cpu"
                )
                return f"⚠️ Using MiniLM embeddings on CPU (BGE model failed: {str(e)[:50]}...)"
            except Exception as e2:
                # Block OpenAI by setting dummy key
                import os
                os.environ["OPENAI_API_KEY"] = "dummy_key_to_prevent_error"
                return f"⚠️ All embedding models failed: {str(e2)[:50]}..."
    else:
        # Block OpenAI by setting dummy key
        import os
        os.environ["OPENAI_API_KEY"] = "dummy_key_to_prevent_error"
        return "⚠️ HuggingFace not available, OpenAI blocked"

def get_llm_status():
    """Get current LLM status for display"""
    if GOOGLE_GENAI_AVAILABLE and os.getenv("GOOGLE_API_KEY"):
        return "🤖 Google Gemini 2.0 Flash (GenAI)"
    else:
        return "❌ Google Gemini not available"

def get_embedding_status():
    """Get current embedding status for display"""
    if HUGGINGFACE_AVAILABLE:
        return "🤖 Local BGE embeddings available"
    else:
        return "⚠️ Using default embeddings"

def setup_llm_and_embeddings():
    """Complete setup of LLM and embeddings"""
    # Initialize LLM
    llm = initialize_llm()
    if llm is None:
        st.stop()
    
    # Initialize embeddings
    embedding_status = initialize_embeddings()
    
    return {
        "llm": llm,
        "embedding_status": embedding_status,
        "llm_status": get_llm_status(),
        "embedding_available": HUGGINGFACE_AVAILABLE
    }

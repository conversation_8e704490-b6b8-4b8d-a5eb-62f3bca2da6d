# Project Management System
import os
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional
import streamlit as st
from config import DATA_DIR, CHROMA_DB_DIR, CACHE_DIR

# Project directories
PROJECTS_DIR = "projects"
PROJECT_CONFIG_FILE = "projects.json"

os.makedirs(PROJECTS_DIR, exist_ok=True)

class ProjectManager:
    def __init__(self):
        self.projects_file = PROJECT_CONFIG_FILE
        self.projects = self.load_projects()
    
    def load_projects(self) -> Dict:
        """Load projects from file"""
        try:
            if os.path.exists(self.projects_file):
                with open(self.projects_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            st.warning(f"⚠️ Could not load projects: {str(e)}")
        return {}
    
    def save_projects(self):
        """Save projects to file"""
        try:
            with open(self.projects_file, 'w', encoding='utf-8') as f:
                json.dump(self.projects, f, indent=2, ensure_ascii=False)
        except Exception as e:
            st.error(f"❌ Could not save projects: {str(e)}")
    
    def create_project(self, name: str, description: str = "") -> str:
        """Create a new project"""
        project_id = str(uuid.uuid4())[:8]  # Short UUID
        
        project_data = {
            "id": project_id,
            "name": name,
            "description": description,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "document_count": 0,
            "chat_count": 0
        }
        
        # Create project directories
        project_dir = os.path.join(PROJECTS_DIR, project_id)
        os.makedirs(project_dir, exist_ok=True)
        os.makedirs(os.path.join(project_dir, "documents"), exist_ok=True)
        os.makedirs(os.path.join(project_dir, "chroma_db"), exist_ok=True)
        os.makedirs(os.path.join(project_dir, "cache"), exist_ok=True)
        
        # Create project-specific files
        with open(os.path.join(project_dir, "chat_history.json"), 'w') as f:
            json.dump([], f)
        with open(os.path.join(project_dir, "document_metadata.json"), 'w') as f:
            json.dump({}, f)
        
        self.projects[project_id] = project_data
        self.save_projects()
        
        return project_id
    
    def get_project(self, project_id: str) -> Optional[Dict]:
        """Get project by ID"""
        return self.projects.get(project_id)
    
    def get_all_projects(self) -> Dict:
        """Get all projects"""
        return self.projects
    
    def update_project(self, project_id: str, **kwargs):
        """Update project data"""
        if project_id in self.projects:
            self.projects[project_id].update(kwargs)
            self.projects[project_id]["updated_at"] = datetime.now().isoformat()
            self.save_projects()
    
    def delete_project(self, project_id: str) -> bool:
        """Delete a project"""
        if project_id in self.projects:
            # Remove project directory
            import shutil
            project_dir = os.path.join(PROJECTS_DIR, project_id)
            if os.path.exists(project_dir):
                shutil.rmtree(project_dir)
            
            # Remove from projects
            del self.projects[project_id]
            self.save_projects()
            return True
        return False
    
    def get_project_paths(self, project_id: str) -> Dict[str, str]:
        """Get all paths for a project"""
        project_dir = os.path.join(PROJECTS_DIR, project_id)
        return {
            "project_dir": project_dir,
            "documents_dir": os.path.join(project_dir, "documents"),
            "chroma_db_dir": os.path.join(project_dir, "chroma_db"),
            "cache_dir": os.path.join(project_dir, "cache"),
            "chat_history_file": os.path.join(project_dir, "chat_history.json"),
            "metadata_file": os.path.join(project_dir, "document_metadata.json"),
            "feedback_file": os.path.join(project_dir, "user_feedback.json"),
            "query_cache_file": os.path.join(project_dir, "cache", "query_cache.json"),
            "response_cache_file": os.path.join(project_dir, "cache", "response_cache.json"),
            "learning_data_file": os.path.join(project_dir, "cache", "learning_data.json")
        }
    
    def get_project_stats(self, project_id: str) -> Dict:
        """Get project statistics"""
        paths = self.get_project_paths(project_id)
        stats = {
            "document_count": 0,
            "chat_count": 0,
            "cache_entries": 0,
            "total_size_mb": 0
        }
        
        try:
            # Count documents
            if os.path.exists(paths["documents_dir"]):
                stats["document_count"] = len([f for f in os.listdir(paths["documents_dir"]) 
                                             if os.path.isfile(os.path.join(paths["documents_dir"], f))])
            
            # Count chat messages
            if os.path.exists(paths["chat_history_file"]):
                with open(paths["chat_history_file"], 'r') as f:
                    chat_history = json.load(f)
                    stats["chat_count"] = len(chat_history)
            
            # Count cache entries
            if os.path.exists(paths["response_cache_file"]):
                with open(paths["response_cache_file"], 'r') as f:
                    cache = json.load(f)
                    stats["cache_entries"] = len(cache)
            
            # Calculate total size
            if os.path.exists(paths["project_dir"]):
                total_size = 0
                for dirpath, dirnames, filenames in os.walk(paths["project_dir"]):
                    for filename in filenames:
                        filepath = os.path.join(dirpath, filename)
                        if os.path.exists(filepath):
                            total_size += os.path.getsize(filepath)
                stats["total_size_mb"] = round(total_size / (1024 * 1024), 2)
        
        except Exception as e:
            st.warning(f"⚠️ Could not calculate stats for project {project_id}: {str(e)}")
        
        return stats

def render_project_selector():
    """Render project selection UI"""
    pm = ProjectManager()
    
    st.sidebar.header("📁 Project Management")
    
    # Project selection
    projects = pm.get_all_projects()
    
    if not projects:
        st.sidebar.info("No projects yet. Create your first project!")
        project_id = None
    else:
        project_options = {f"{proj['name']} ({pid})": pid for pid, proj in projects.items()}
        selected_option = st.sidebar.selectbox(
            "Select Project:",
            options=list(project_options.keys()),
            key="project_selector"
        )
        project_id = project_options.get(selected_option) if selected_option else None
    
    # Project creation
    with st.sidebar.expander("➕ Create New Project"):
        new_name = st.text_input("Project Name:", key="new_project_name")
        new_description = st.text_area("Description (optional):", key="new_project_desc", height=60)
        
        if st.button("Create Project", type="primary"):
            if new_name.strip():
                new_project_id = pm.create_project(new_name.strip(), new_description.strip())
                st.success(f"✅ Project '{new_name}' created!")
                st.session_state.project_selector = f"{new_name} ({new_project_id})"
                st.rerun()
            else:
                st.error("❌ Project name is required!")
    
    # Project management
    if project_id:
        project = pm.get_project(project_id)
        stats = pm.get_project_stats(project_id)
        
        with st.sidebar.expander("📊 Project Info"):
            st.write(f"**Name:** {project['name']}")
            if project.get('description'):
                st.write(f"**Description:** {project['description']}")
            st.write(f"**Created:** {project['created_at'][:10]}")
            st.write(f"**Documents:** {stats['document_count']}")
            st.write(f"**Chat Messages:** {stats['chat_count']}")
            st.write(f"**Cache Entries:** {stats['cache_entries']}")
            st.write(f"**Size:** {stats['total_size_mb']} MB")
        
        # Project actions
        with st.sidebar.expander("⚙️ Project Actions"):
            if st.button("🗑️ Delete Project", key="delete_project"):
                if st.session_state.get("confirm_delete"):
                    pm.delete_project(project_id)
                    st.success(f"✅ Project '{project['name']}' deleted!")
                    if "project_selector" in st.session_state:
                        del st.session_state.project_selector
                    st.rerun()
                else:
                    st.session_state.confirm_delete = True
                    st.warning("⚠️ Click again to confirm deletion!")
    
    return project_id, pm

def get_current_project_paths():
    """Get paths for the currently selected project"""
    if "current_project_id" not in st.session_state:
        return None
    
    pm = ProjectManager()
    return pm.get_project_paths(st.session_state.current_project_id)

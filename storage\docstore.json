{"docstore/metadata": {"e8d164ca-e06a-431d-b298-17467a15ca19": {"doc_hash": "764de807b65ea2118b50826f0bf8e36a2cf5ef7184f342747d255b7084f6531e"}, "62c0a158-9508-48c7-a1bd-d4b5e0c305e1": {"doc_hash": "6e25ad0d887f57d3027006db1b28386aee28e9e06a7ec30c1adbc1dc3a9d6c4b"}, "03cec759-3cf1-4617-8e4f-605ba8a97277": {"doc_hash": "bf1a2049f66bd880d6c3a71a6a898b16b4b662c71aa23b691dbf2ca8d03c758b"}, "5a977cf6-5483-45c6-b9d4-d8e024132da9": {"doc_hash": "ea44a9550bcf1092e4b9012287b80d2ac6e49c954de043b4c3ab3ff1ca82502f"}, "d2917da7-ba95-4753-b09b-51b0ca388b34": {"doc_hash": "d93b796c009512b095e090327252bfdca283595f92cb45fb5c69d0dea7bd4c73"}, "07f452a3-18ac-4314-b38a-bf19c9c9a3c8": {"doc_hash": "a4a45516b0cea3620e23e959aa75952818c54664940d9ab2f799288edab2ce94"}, "d9eae386-5f7d-49d3-a479-5f1d1fccc254": {"doc_hash": "0e57d732144de99bd9526835e682af36e49a0c530488fe34f5fecc1852435b03"}, "6e50b4db-a758-4431-b1a4-2e74cbc5fc72": {"doc_hash": "645788a955474cc0d5bbc29267a58e8d02a03e90804a4036d6253ef44c01339d"}, "61ac8878-6b6f-4039-8b3c-5557469478fb": {"doc_hash": "86c216362283ad1d1cf76392bceb6be709414135ea6df0f1d7c8b90a0803f9f2"}, "4adf43d7-2a09-48df-a35d-5aad35dd8e00": {"doc_hash": "1dfacb70b2f635a211ba67b77de2ca8e305b44b178bb98f4e242c0020e79e01c"}, "a13ce568-687d-4fc0-b2bd-03c2dad7e14e": {"doc_hash": "89dcd98d0119cd628ac8244441eab4832d941cd2e9cbaf8b841ac849100a1c4a"}, "327f3902-62b6-4229-b672-d35ad99911a2": {"doc_hash": "fe814b0f9a9ba51a686decba0fe2f74cd9ce6a0ebc5b49b7947d2fc447c93ac9"}, "9624ec0a-d0fb-44af-b755-1304ee3c73ee": {"doc_hash": "47055333309f3275d391987673ffc01004275822a7cc02603532a4350d11d6b2"}, "7b3730fa-fb3e-4721-9760-f2d2f9fddddc": {"doc_hash": "13e832b957684c2c42fa621821be941b48e07b3e391a9d08437e8267209952b5"}, "450f1d04-7baa-4d39-a638-4a709ea085dd": {"doc_hash": "d4dfce89ca45096a4fa5f8b458e5e13d305ba218acda9d693b4edc434344fe49"}, "9010b59d-6d1a-40f8-a336-82bf3ea93e5b": {"doc_hash": "5edec4120cb9f57a89482ae4974263c5195dfc8de2a15c4972b9fcaa1929fbc9"}, "a99437ac-950d-4aad-b8b9-c9a52f997299": {"doc_hash": "a217d4fae42149ad0cd86aed861351f8edd122c458073acf6b6094f48532f4f0"}, "8e3d120d-68ed-450f-a3a2-f430922d0e14": {"doc_hash": "07847f81ce34becbc106bdb2c9ecae86e64dea188ba6388aff683e89fe65eb75"}, "6c3e05a0-4311-43af-8b46-ba7532917d6f": {"doc_hash": "eb7b3a79b7e64af1bf5a6d6bb6eb6d185c815f742dc6cbc9be54ba4346c64ca2"}, "2144aefc-b399-4ea0-8ec9-bb6324e8d1eb": {"doc_hash": "9ab4e8ec575e88ff9da57c1dd6b64721d839a768fe947f6275a673c6e7cac504"}, "a1ee8109-5f8f-4e2e-b736-686264825d8d": {"doc_hash": "c6ca73b250fad0c40f0b7b1c6e5ed3ae298d5d0914adb4040ae79eaccc62a5bd"}, "6a6bcc5a-0bd4-407a-9d74-718427c79c19": {"doc_hash": "c360ac0b41e65593d10674d20f013f50d7657039ceed6de0d61886cdb455ed0f"}, "dc47a7a1-14b8-42e1-b66f-2b2837ecd5b4": {"doc_hash": "b9d2629782902d7c36309edc9a8036817cf265f6dd95ea7b67568895568f3f64"}, "4f871724-bd44-499a-af26-6fb0fe053ef3": {"doc_hash": "fc3d36fa1060092e8cdbcd033415be1f7bee9f55b98eaf0fa369bae4786dd9d6"}, "fcce2a2f-7f38-4f2a-943a-67506fa80ae0": {"doc_hash": "f836c3504db58324cbb79c80f0bd75e7b61337928585f680fca274f1562c0a79"}, "df95b1c3-14a4-451f-8c6c-883a9ed62b67": {"doc_hash": "516fbb1a15fccbe16796c548353ca53edfd6add212ee4656c9a6bbfe9201c881"}, "e56fbb7d-f5f4-4147-b65e-9cf7630dafd4": {"doc_hash": "7d40a660d893d769dbe13e46320c1d464580699cc2cfce72b70c52e875061ba4"}, "aaa61bd5-f7d2-43c2-84f5-a093410638aa": {"doc_hash": "f0f2af5bfdb27450672ffbcda9eb39bb0362d3a7c3769b7c0b9aef5f0cd51ed3"}, "43e75014-6d93-4c1a-a869-e26d6df8aac2": {"doc_hash": "c2df86d22396ad73d344309556083042d377477ab492c65ed32709a4ee3a643d"}, "2ff2f7bd-3727-4bc7-8c10-6b4f8e764bd0": {"doc_hash": "f96731af5f8da88073156fedc5dc5cd11c4012258922fd8b30216fa10c986c29"}, "bf50f4ad-d371-415b-bd50-13f665f99ec3": {"doc_hash": "baf283bfaff2a9dabc04aecfeb4fca14cff020f5b149f94083554c2430deee97"}, "e350bcb1-e8e7-4ba2-b32b-63ac62c39a39": {"doc_hash": "110f8ae37829012ec453c57a33336e656d1b5ea0df8655be62fdd574d23845a2"}, "af107a73-6b56-458a-9cc6-9c6ae0e4271e": {"doc_hash": "e66e9f1c40e4edce2d49473b128b791022a25c43d21cf0d878fd56455df2b506"}, "492ee8a7-4a31-4f2b-99fb-f87a7c95f908": {"doc_hash": "a546b1acd6914cbe5a1613c389680002f1c554105fba1a8d6a6c812ec85ce76a"}, "2476d2f1-5efb-417d-a974-c73b2c9b9198": {"doc_hash": "17b27dbc6926e1847cb07f6d0a11caf8fac728652022d5df968dcd8d94de4fda"}, "8b56084b-33eb-4618-9d7e-940b55c064e9": {"doc_hash": "8cd88a552ad5722860066279e04c65ef7377c5cc6545b35486368d9a2347bed6"}, "aaad3539-51fb-4260-af6d-b263ab3b54c1": {"doc_hash": "4e6ed41187b6ea649c553acbca7b31a0fe5cc24ab20f7e64faa2d1490d006581"}, "1010dc03-8934-4630-9a9a-3c1fd0d5016b": {"doc_hash": "b909edffe17d72fe81c196b919646937f9a2d6758ad3d3685abd5b8ce4daa1c7"}, "4d58af98-7f63-4338-a965-cc9198bef03f": {"doc_hash": "021a063153acb63c6ed4cfc2c97b0a802d898c0cec2d4bdb6db94feee24d0bb3"}, "bfb76a06-5b14-4d1e-b839-bb655dfc065e": {"doc_hash": "716b38d8a232e97c73d5737d0e327c3152db8564d28dd8dce41a8e493b30cb15"}, "fac977ee-17f9-4b6b-9480-3139769ffa81": {"doc_hash": "0015e910161596cb7192f4abedad8a06296071c1530c493c7dc434dddae1fb61"}, "e59096c7-e7ee-40d1-b3be-b0c0d2781859": {"doc_hash": "9e39395c4d59a32f139078eadc0b04bf611b7fd9d9f1669ea636860a3580f396"}, "174a6149-519d-4cec-87b2-8631a325265a": {"doc_hash": "4b9ce9c2a4fefbdcb1d5d656d40392a08931684a75922c0a602a69b29d049d4c"}, "bec400f3-5dae-4f8d-a463-8b736db3dd76": {"doc_hash": "b6572de453a3512e49101069c73a77a58831f0511ac1d3ad7ecd83321d1566df"}, "ea22cb8e-6005-4e58-a102-fee67ed9d335": {"doc_hash": "07ead61817eac2087d4e7a8583a89e2e76bca1777cfa52997d6b8567f489a728"}, "9610743f-c976-44d9-a0fd-************": {"doc_hash": "8ba2af04d0381877bc9d2fd7f20d72a009209ca0cd85a2b7c656b7187c981f6a", "ref_doc_id": "e8d164ca-e06a-431d-b298-17467a15ca19"}, "877b6830-713c-45a3-b987-c6182194ae98": {"doc_hash": "596d2fb43f5719560efaffbf1e9cfa4ac57bb7c42bb2b8d8b9b33be3088a2101", "ref_doc_id": "62c0a158-9508-48c7-a1bd-d4b5e0c305e1"}, "6a531744-c165-4fee-83cf-fd7c5f5e694e": {"doc_hash": "865c55ca8d2494bb1e9ac289035b726212435940303018240b431451d1260b35", "ref_doc_id": "62c0a158-9508-48c7-a1bd-d4b5e0c305e1"}, "d988cfa9-5740-496f-ace6-4a1051cfaa06": {"doc_hash": "e6fffb0cf8966500e3dabce61517abbe456d7fde3baf976f4f3e49f02acd7ee4", "ref_doc_id": "03cec759-3cf1-4617-8e4f-605ba8a97277"}, "970c0f80-f557-4cb2-a835-2418ff55783b": {"doc_hash": "079c40911727ec938184a447df14c38b732175e13ed33d6c67894a22e9b93dfb", "ref_doc_id": "5a977cf6-5483-45c6-b9d4-d8e024132da9"}, "44ba16ec-3403-4b9d-82ab-46b45c226ca6": {"doc_hash": "553dd0b89f106c4ec0fcb77f60f9767688bfba067de0771532ab2a1077cdcfd1", "ref_doc_id": "d2917da7-ba95-4753-b09b-51b0ca388b34"}, "ad5bbaba-4c13-4aa0-99e2-bb9034d43169": {"doc_hash": "f5f188d40bdb8e00a96c46ecbd0a16c176de2ffdbcafa795b11af1630ef7052c", "ref_doc_id": "07f452a3-18ac-4314-b38a-bf19c9c9a3c8"}, "d9ea4a07-354b-41b4-b414-752c92ea04d0": {"doc_hash": "1f1cf8a3a51786dcc0391b7c0ba6803ce997756276b6d561a9526dda03e1fc83", "ref_doc_id": "d9eae386-5f7d-49d3-a479-5f1d1fccc254"}, "41455b7e-5d66-4cfe-a037-ac33ac210cb9": {"doc_hash": "3488c162ff9b1618e6480b22241a450375584c9701a186f452d48200ba644df6", "ref_doc_id": "6e50b4db-a758-4431-b1a4-2e74cbc5fc72"}, "d2b1e02f-7084-45ad-9840-9775c31c2add": {"doc_hash": "7c171c45bc378e404eca47c33f2837e2449cd5cb5926fd7c864bd048f579f3a9", "ref_doc_id": "61ac8878-6b6f-4039-8b3c-5557469478fb"}, "999b634a-3943-45f5-9de2-84c80346b14f": {"doc_hash": "e3b793114cd58be6765ad19a777b378d3990344689ded4c9dedffac95900a389", "ref_doc_id": "4adf43d7-2a09-48df-a35d-5aad35dd8e00"}, "8007dc05-096c-4966-878a-a60341eceaa0": {"doc_hash": "a77cc21435ea7145c80855409b6587e20f0b11f2bb7b13b511b869e6a7e885a8", "ref_doc_id": "a13ce568-687d-4fc0-b2bd-03c2dad7e14e"}, "4f31f787-9d6c-43db-8c68-d8c6962b587a": {"doc_hash": "62325dc41b8dbb53907b88765995daf572e542ede1db2d13d45231fc80230b9a", "ref_doc_id": "327f3902-62b6-4229-b672-d35ad99911a2"}, "4fbbcdfc-bb7c-451b-81e3-21f9353b88cc": {"doc_hash": "59f0baa53df2a126ffc8b3b7f2b0c037a985edf7e0aa48c805956c3470e51a6f", "ref_doc_id": "9624ec0a-d0fb-44af-b755-1304ee3c73ee"}, "d2907336-9ca0-4eab-b643-0efba4c349a3": {"doc_hash": "1a1449092cc8640a34759cc1f4f1790476395de6939cfcfa2e77514add693f9a", "ref_doc_id": "7b3730fa-fb3e-4721-9760-f2d2f9fddddc"}, "51ce4602-bdda-4da5-8416-29d3358d54bb": {"doc_hash": "7e58367c0bf8668e47f7ee9ef6539472c02c3bcdeb21d7dd061d0367b4e305b7", "ref_doc_id": "450f1d04-7baa-4d39-a638-4a709ea085dd"}, "71806e9f-825d-4ced-967f-3243bd8cbbe5": {"doc_hash": "68520c520e2b4a219d8cb2c75c610eb5f3b7a5b7b3f9e2fb13a54da4246a743f", "ref_doc_id": "9010b59d-6d1a-40f8-a336-82bf3ea93e5b"}, "909a2b85-8707-4c86-a23f-309cd3d65ef6": {"doc_hash": "f6e33f042dc13380d31e45a86e14742939e43f8fa584d7a0cc4f9993a1726101", "ref_doc_id": "a99437ac-950d-4aad-b8b9-c9a52f997299"}, "f069fdc4-b5d7-4efa-ad8f-6d0d478669e6": {"doc_hash": "9924d9cb2c0a1afaeb23204865fa9a60d4363692e81f2bcbb43c2fe40ce62bd3", "ref_doc_id": "8e3d120d-68ed-450f-a3a2-f430922d0e14"}, "f757713e-1630-4126-a2b6-60511ae91723": {"doc_hash": "cb739b085aa40a9f3ccf43e8af14941b412e34537134236bc932b7554f017163", "ref_doc_id": "6c3e05a0-4311-43af-8b46-ba7532917d6f"}, "87272613-af6e-4abe-987c-ad71e3d10e79": {"doc_hash": "e4201edbb78ba9e0f3aeb3897b9bf79cd7b63d5e63036c669aa54b9c045ec6a0", "ref_doc_id": "2144aefc-b399-4ea0-8ec9-bb6324e8d1eb"}, "498301b9-cf96-4423-8679-46efbf76f488": {"doc_hash": "fec219b6619bb1b6a2972691beb45c1e7ee5bc045dfe78d801e3f171c0750233", "ref_doc_id": "a1ee8109-5f8f-4e2e-b736-686264825d8d"}, "1fa16851-f4c4-4702-8edb-908f25bb0de3": {"doc_hash": "478e521b94c2b17f2be9558801c70d59f8775d9852c10cbd818bcb1b9dcc67f1", "ref_doc_id": "6a6bcc5a-0bd4-407a-9d74-718427c79c19"}, "da6d4d86-5e1d-404f-acfb-1c8d929c1a69": {"doc_hash": "ebaba5b313ef15f2f1623a3dd048cb793b74e3a66d24e8acd956ae8ffb0eda0a", "ref_doc_id": "dc47a7a1-14b8-42e1-b66f-2b2837ecd5b4"}, "caf7b161-f106-43cb-bd7f-ffb3e41f5392": {"doc_hash": "5b98791e2fe9c7bfffeccbd9867ed480ca48e2c80ede7ff373c673bcb56f3d06", "ref_doc_id": "4f871724-bd44-499a-af26-6fb0fe053ef3"}, "ad28c9c3-9998-47c6-84a0-d7935d58cace": {"doc_hash": "4316c09e35ffab2e1bcb6ab4bcc0e85195aa39ebe3bde8a03a5ea762a38d28b7", "ref_doc_id": "fcce2a2f-7f38-4f2a-943a-67506fa80ae0"}, "8422aec1-ba6a-4e9a-839d-64c5af06b066": {"doc_hash": "4bbe5f7a451f55eca7a82923b947623310258a90471ad513156453691d9b48ad", "ref_doc_id": "df95b1c3-14a4-451f-8c6c-883a9ed62b67"}, "af0e4d59-1c51-41d6-8195-b361de7781ec": {"doc_hash": "80b520f023d62492a0b9bdd2be382605d9ff2235cc14755938fd52d90d45cca0", "ref_doc_id": "e56fbb7d-f5f4-4147-b65e-9cf7630dafd4"}, "bbe148aa-4808-4139-a452-a9a9b99aacec": {"doc_hash": "343b22bf4115bfb11c7321acccc81486fd1d268d4c16cb16e9ea2b6d5256ae8b", "ref_doc_id": "aaa61bd5-f7d2-43c2-84f5-a093410638aa"}, "c6da6039-6f7d-4af8-8515-2e4fb76f2dd1": {"doc_hash": "d705664c014c4980aeaac8039c1319c60e3bd6278d46d98d49c906cba7520ca5", "ref_doc_id": "43e75014-6d93-4c1a-a869-e26d6df8aac2"}, "910013af-97b4-4dca-a7c6-28a5a315b595": {"doc_hash": "1585e16748e0a8a4f17e08e7aa325e0ffdac0fc10858d1ac87987a3c45f13595", "ref_doc_id": "2ff2f7bd-3727-4bc7-8c10-6b4f8e764bd0"}, "3e7de4a9-2b51-47b1-9802-b15ca7eb102f": {"doc_hash": "d28759ed19352bf5f9d91457ba4093b031ae33cfca111b3da7a0b632628be6ba", "ref_doc_id": "bf50f4ad-d371-415b-bd50-13f665f99ec3"}, "8abf17de-2e47-4ab0-a121-f0b9b4e2abbb": {"doc_hash": "2727b1a76f484347e72df166050ad27628795c0e3c37bf512536d3c45495f3c7", "ref_doc_id": "e350bcb1-e8e7-4ba2-b32b-63ac62c39a39"}, "e9d58db3-f5e9-427d-bc47-ebca4ddee3c4": {"doc_hash": "52bb0c41f5e5eb9ad228cd4114e54e0244498a136fb4914854805422b228ade5", "ref_doc_id": "af107a73-6b56-458a-9cc6-9c6ae0e4271e"}, "1dfaa977-14d9-413d-85d9-b7b81dbcdf28": {"doc_hash": "dd9bc7c7ca5d844ffddfb2d52f4cf910da74ac00f4603c9dc70f5017c0b87b3f", "ref_doc_id": "492ee8a7-4a31-4f2b-99fb-f87a7c95f908"}, "5e742965-a09b-46e3-afb4-3b70adcf4d20": {"doc_hash": "c8b7342a43d7f9526a6f5908903e94df2565372fda4a4f6a4c4caeafa55b2dc4", "ref_doc_id": "2476d2f1-5efb-417d-a974-c73b2c9b9198"}, "c1c1580b-839c-4c61-bbf0-aca921e0e8ae": {"doc_hash": "abb0db5875ba7430aa42c1c293024cc0bfe4871902699bb6f218b049c695d4b2", "ref_doc_id": "8b56084b-33eb-4618-9d7e-940b55c064e9"}, "d4f634ca-7e45-4d1d-b773-991c909f7e21": {"doc_hash": "94800a84174693c4da0d7bbd7f49bce919c66c6d6635c17fefb7a71d92b021c3", "ref_doc_id": "aaad3539-51fb-4260-af6d-b263ab3b54c1"}, "4fb3d105-d7c8-46ed-9aa7-9537b1cd2981": {"doc_hash": "95182a360ffdd8672341a9d84afa5515bf9b8b14d45edd5f5a73b0c510967351", "ref_doc_id": "1010dc03-8934-4630-9a9a-3c1fd0d5016b"}, "e683987b-9321-4fba-8ef7-c6b2a6b195dc": {"doc_hash": "c52ff5cc9b9e1acd892f0a4f86832c2c4f1859b3d221ed380d2da108498dc035", "ref_doc_id": "4d58af98-7f63-4338-a965-cc9198bef03f"}, "576fff02-5a7c-4171-b3f3-b8d04e6f7370": {"doc_hash": "38813d90f6d05734d051045d6b0088824d1a96110de440879bee55cacc423898", "ref_doc_id": "bfb76a06-5b14-4d1e-b839-bb655dfc065e"}, "2db4d935-bf6b-4068-89a9-2cdfd590d888": {"doc_hash": "92bf7185764f51c8f1357d07b8cc1347dbd6c49a362fabc7add105eb207a4514", "ref_doc_id": "fac977ee-17f9-4b6b-9480-3139769ffa81"}, "ac736d44-cba6-4ad0-9cf6-59d61d05c5f3": {"doc_hash": "9afd9769915437fc2b56503c32bf1e18dc1bd045481fab68176d4f633a8979f8", "ref_doc_id": "e59096c7-e7ee-40d1-b3be-b0c0d2781859"}, "f54a3a59-d794-4e97-b52d-381dfb578e2d": {"doc_hash": "edbe04c7a9918439e84b40e9e85be6b5ecd438183fb8bb1395b35aa35392d212", "ref_doc_id": "174a6149-519d-4cec-87b2-8631a325265a"}, "ba5bfa75-c013-4473-9522-6fe3926dedb9": {"doc_hash": "4648aef66c825c70d4de378ab79fe37e1532d7c9594b7650e8a54a3844c05c50", "ref_doc_id": "bec400f3-5dae-4f8d-a463-8b736db3dd76"}, "4c788242-3e8c-4113-8ea9-f9baaf5f000b": {"doc_hash": "81147bf9439f56bc67862697d5149befb27a88a6f87945ef0d7e9dc7769084ee", "ref_doc_id": "ea22cb8e-6005-4e58-a102-fee67ed9d335"}}, "docstore/data": {"9610743f-c976-44d9-a0fd-************": {"__data__": {"id_": "9610743f-c976-44d9-a0fd-************", "embedding": null, "metadata": {"page_label": "1", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "e8d164ca-e06a-431d-b298-17467a15ca19", "node_type": "4", "metadata": {"page_label": "1", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "764de807b65ea2118b50826f0bf8e36a2cf5ef7184f342747d255b7084f6531e", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS \nPART 1 \nROADS \n \nCHAPTER 2 - EART<PERSON><PERSON>ORKS \n \n \n \n \n \n \n \nDOCUMENT NO: TR-542-1 \nSECOND EDITION \nSEPTEMBER 2020", "mimetype": "text/plain", "start_char_idx": 12, "end_char_idx": 158, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "877b6830-713c-45a3-b987-c6182194ae98": {"__data__": {"id_": "877b6830-713c-45a3-b987-c6182194ae98", "embedding": null, "metadata": {"page_label": "2", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "62c0a158-9508-48c7-a1bd-d4b5e0c305e1", "node_type": "4", "metadata": {"page_label": "2", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "6e25ad0d887f57d3027006db1b28386aee28e9e06a7ec30c1adbc1dc3a9d6c4b", "class_name": "RelatedNodeInfo"}, "3": {"node_id": "6a531744-c165-4fee-83cf-fd7c5f5e694e", "node_type": "1", "metadata": {}, "hash": "ff9b7ec509d0dd09b4e487d8d106c047287977292783d025ca3c6c93959c18a5", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage i \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \n \nCHAPTER 2: Earthworks \nTABLE OF CONTENTS \nTable of Contents................................ ................................ ................................ ............................. i \nList of Tables ................................ ................................ ................................ ................................ .. iii \n2 Earthworks ................................ ................................ ................................ ........................ 2-1 \n2.1 General ................................ ................................ ................................ .......................... 2-1 \n2.1.1 Description ................................ ................................ ................................ ................. 2-1 \n2.1.2 Reference Standards and Codes ................................ ................................ ............... 2-1 \n2.1.3 Contractor's Responsibility ................................ ................................ ......................... 2-4 \n2.1.3.1 Site Visit ................................ ................................ ................................ .......... 2-4 \n2.1.3.2 Boreholes ................................ ................................ ................................ ........ 2-4 \n2.2 Clearing and Grubbing ................................ ................................ ................................ ... 2-7 \n2.2.1 Description ................................ ................................ ................................ ................. 2-7 \n2.2.2 Construction Requirements ................................ ................................ ........................ 2-7 \n2.2.2.1 Areas to be Cleared, Grubbed, and Removed of Topsoil ................................ .", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1906, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "6a531744-c165-4fee-83cf-fd7c5f5e694e": {"__data__": {"id_": "6a531744-c165-4fee-83cf-fd7c5f5e694e", "embedding": null, "metadata": {"page_label": "2", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "62c0a158-9508-48c7-a1bd-d4b5e0c305e1", "node_type": "4", "metadata": {"page_label": "2", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "6e25ad0d887f57d3027006db1b28386aee28e9e06a7ec30c1adbc1dc3a9d6c4b", "class_name": "RelatedNodeInfo"}, "2": {"node_id": "877b6830-713c-45a3-b987-c6182194ae98", "node_type": "1", "metadata": {"page_label": "2", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "596d2fb43f5719560efaffbf1e9cfa4ac57bb7c42bb2b8d8b9b33be3088a2101", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "2-7 \n2.2.2.2 Clearing ................................ ................................ ................................ ........... 2-8 \n2.2.2.3 Grubbing ................................ ................................ ................................ .......... 2-8 \n2.2.2.4 Conservation of Existing Topsoil, Vegetation, and Cutting of Trees ................. 2-8 \n******* Removal and Salvage of Topsoil ................................ ................................ ..... 2-8 \n2.2.2.6 Roadside Cleanup ................................ ................................ ........................... 2-8 \n2.2.2.7 Disposal of Material ................................ ................................ ......................... 2-9 \n2.3 Removal of Structures and Obstructions ................................ ................................ ........ 2-9 \n2.3.1 Description ................................ ................................ ................................ ................. 2-9 \n2.3.2 Construction Requirements ................................ ................................ ........................ 2-9 \n2.3.2.1 General ................................ ................................ ................................ ............ 2-9 \n2.3.2.2 Removal of Structures and Foundations ................................ .......................... 2-9 \n2.3.2.3 Removal of Bridges, Culverts, and Drainage Structures ................................  2-10 \n2.3.2.4 Removal of Pavement, Barriers, Railings, Sidewalks, Kerbs, and Gutters ..... 2-10 \n2.3.2.5 Disposal of Materials ................................ ................................ ..................... 2-11 \n2.4 Roadway Excavation ................................ ................................ ................................ ... 2-11 \n2.4.1 Description ................................ ................................ ................................ ............... 2-11 \n2.4.2 Construction Requirements ................................ ................................ ...................... 2-12 \n2.4.2.1 Preparation for Roadway Excavation ................................ ............................. 2-12 \n2.4.2.2 Control of Excavation ................................ ................................ ..................... 2-12 \n2.4.2.3 Dewatering ................................ ................................ ................................ .... 2-12 \n******* Rock Cuts ................................ ................................ ................................ ...... 2-15 \n2.4.2.5 Subexcavation ................................ ................................ ...............................  2-19 \n2.4.2.6 Slope Treatment ................................ ................................ ............................ 2-20 \n******* Disposal of Unsuitable or Excess Material ................................ ..................... 2-20 \n2.4.2.8 Wasting Material ................................ ................................ ............................ 2-20 \n2.4.2.9 Roadway Ditch and Pond Excavation ................................ ............................ 2-20 \n2.4.2.10 Borrow Excavation ................................ ................................ ......................... 2-21 \n2.4.2.11 Structural Excavation ................................ ................................ ..................... 2-21 \n2.4.2.12 Manual Excavation ................................ ................................ ........................ 2-22 \n2.4.2.13 Unclassified Excavation ................................ ................................ ................. 2-23 \n2.5 Embankments and Backfill ................................ ................................ ........................... 2-24 \n2.5.1 Description ................................ ................................ ................................ ............... 2-24 \n2.5.2 Materials ................................ ................................ ................................ .................. 2-24 \n2.5.2.1 Unsuitable Material ................................ ................................ ........................ 2-24 \n******* Embankment Material (Load-Bearing) ................................ ........................... 2-24 \n******* Rock Fill Material ................................ ................................ ........................... 2-25 \n******* Structural Backfill ................................ ................................ ........................... 2-25", "mimetype": "text/plain", "start_char_idx": 1907, "end_char_idx": 6478, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d988cfa9-5740-496f-ace6-4a1051cfaa06": {"__data__": {"id_": "d988cfa9-5740-496f-ace6-4a1051cfaa06", "embedding": null, "metadata": {"page_label": "3", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "03cec759-3cf1-4617-8e4f-605ba8a97277", "node_type": "4", "metadata": {"page_label": "3", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "bf1a2049f66bd880d6c3a71a6a898b16b4b662c71aa23b691dbf2ca8d03c758b", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage ii \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n******* Pervious Backfill ................................ ................................ ............................ 2-26 \n******* Material for Free-Draining Blankets................................ ................................  2-27 \n2.5.2.7 Non-Load-Bearing Material ................................ ................................ ............ 2-27 \n2.5.2.8 Prefabricated Vertical Drains ................................ ................................ ......... 2-28 \n******* Construction Geotextile................................ ................................ .................. 2-29 \n2.5.2.10 Surface Protection ................................ ................................ ......................... 2-32 \n2.5.2.11 Water ................................ ................................ ................................ ............. 2-32 \n2.5.3 Construction Requirements ................................ ................................ ...................... 2-32 \n******* Embankment Construction ................................ ................................ ............. 2-32 \n2.5.3.2 Structural Backfill ................................ ................................ ........................... 2-34 \n******* Non-Load-Bearing Fill Placement ................................ ................................ .. 2-34 \n******* Compacting Embankments ................................ ................................ ............ 2-34 \n******* Prefabricated Vertical Drains ................................ ................................ ......... 2-36 \n2.5.3.6 End Caps and Draw Lines for Ducts, Conduits, Pipe Sleeves and Culverts ... 2-37 \n2.6 Subgrade Preparation ................................ ................................ ................................ .. 2-37 \n2.6.1 Traffic Pavement ................................ ................................ ................................ ...... 2-37 \n2.6.2 Pedestrian Pavement ................................ ................................ ...............................  2-37 \n2.7 Geotextile Installation ................................ ................................ ................................ ... 2-37 \n2.7.1 Description ................................ ................................ ................................ ............... 2-37 \n2.7.2 Materials ................................ ................................ ................................ .................. 2-38 \n2.7.3 Construction Requirements ................................ ................................ ...................... 2-38 \n2.7.3.1 Subsurface Drainage and Soakaways ................................ ........................... 2-38 \n2.7.3.2 Permanent Erosion Control and Ditch Lining ................................ ................. 2-39 \n2.8 Trimming and Cleanup ................................ ................................ ................................ . 2-39 \n2.8.1 Description ................................ ................................ ................................ ............... 2-39 \n2.8.2 Construction Requirements ................................ ................................ ...................... 2-39 \nIndex ………………………………………………………………………………………… ................ 2-41", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3406, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "970c0f80-f557-4cb2-a835-2418ff55783b": {"__data__": {"id_": "970c0f80-f557-4cb2-a835-2418ff55783b", "embedding": null, "metadata": {"page_label": "4", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "5a977cf6-5483-45c6-b9d4-d8e024132da9", "node_type": "4", "metadata": {"page_label": "4", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "ea44a9550bcf1092e4b9012287b80d2ac6e49c954de043b4c3ab3ff1ca82502f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage iii \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nLIST OF TABLES \nTable 2-1: Designations and titles for AASHTO and ASTM standards that apply to earthworks ... 2-2 \nTable 2-2: Designations and titles for BS and BS EN standards that apply to earthworks ............ 2-4 \nTable 2-3: Structural backfill gradation ................................ ................................ ....................... 2-25 \nTable 2-4: Pervious backfill gradation ................................ ................................ ........................ 2-26 \nTable 2-5 Pervious backfill material for use as drain age layer against soil retaining structures such \nas bridge abutments and retaining walls ................................ ................................ .................... 2-26 \nTable 2-6:  Gradation for free-draining blanket material ................................ ............................. 2-27 \nTable 2-7:  Vertical filter drain properties ................................ ................................ .................... 2-28 \nTable 2-8: Geotextile for underground drainage ................................ ................................ ......... 2-30 \nTable 2-9: Geotextile for underground filtration properties ................................ .......................... 2-30 \nTable 2-10: Geotextile for separation or soil stabilisation ................................ ........................... 2-30 \nTable 2-11: Geotextile properties for retaining walls and reinforced slopes ................................  2-31 \nTable 2-12: Embankment material and compaction tests ................................ ........................... 2-35", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1721, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "44ba16ec-3403-4b9d-82ab-46b45c226ca6": {"__data__": {"id_": "44ba16ec-3403-4b9d-82ab-46b45c226ca6", "embedding": null, "metadata": {"page_label": "5", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "d2917da7-ba95-4753-b09b-51b0ca388b34", "node_type": "4", "metadata": {"page_label": "5", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "d93b796c009512b095e090327252bfdca283595f92cb45fb5c69d0dea7bd4c73", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-1 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n2 EARTHWORKS \n2.1 General \n2.1.1 Description \nThis section describes the general provisions under which earthworks shall be performed: \n1. Prior to the start of earthwork, the Contractor shall perform all control surveying in accordance\nwith Section 1.2.6. And submit to the Engineer for review and approval. Limits of areas to be \nprotected shall be surveyed and p rotective fenced installed. Excavation and backfill control \nstaking shall be surveyed and placed. \n2. Prior to start of earthwork, the Contractor shall take cross -sections of existing ground every\n25 m jointly with the Engineer, in accordance with the requirements of Section 1.7 of Chapter \n1, General Requirements , and as agreed to by the Engineer. Cross -sections shall have \nsupplemental additional topographic information, including locations of all existing surface \nfeatures in the plan to include structures, fences, utility risers and markers, utility chambers, \ndrainage structures, areas of vegetation and trees, etc., all located to x and y coordinates \nand labelled with surface elevations, as per the Engineer’s instructions. \n3. Use all necessary precautionary and pr otective measures required to maintain existing\nutilities, services, and appurtenances that must be kept in operation. In particular, the \nContractor shall take adequate measures to prevent undermining of utilities and services \npresently in service. \n4. Earthwork shall be constructed to the lines, grades, elevations, slopes, and cross -sections\nindicated on the Contract plans. \n5. All excavation and backfilling shall be performed in dry condition, sunless otherwise directed\nby the Engineer. \n6. All excavation and backfill work, including the associated work performed in accordance with\nthese specifications, Contract plans , and particular specifications, and as instructed or \napproved by the Engineer. \n7. Contractor shall ensure the removal and diversion of underground and surface water from all\nexcavations and finished surfaces. \n8. Contractor to finish the work with final trimming and grading of all surfaces and removal of\nany remaining construction debris and waste materials \n2.1.2 Reference Standards and Codes \nStandards and codes for earthworks shall be as specified in these specifications, in the Contract \ndocuments, if any, and the following, in their latest edition: \nAASHTO LRFD American Association of State Highway and Transportation Officials - Load and \nResistance Factor Design, Bridge Construction Specifications; \nAASHTO LRFD American Association of State Highway and Transportation Officials - Load and \nResistance Factor Design, Bridge Design Specifications; \nAASHTO Standard Specifications for Transportation Materials and Methods o f Sampling \nand Testing; \nASTM American Society for Testing and Materials; \nAD EHSMS RF Abu Dhabi Environment, Health and Safety Management System Regulatory \nFramework Manual;", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3010, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ad5bbaba-4c13-4aa0-99e2-bb9034d43169": {"__data__": {"id_": "ad5bbaba-4c13-4aa0-99e2-bb9034d43169", "embedding": null, "metadata": {"page_label": "6", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "07f452a3-18ac-4314-b38a-bf19c9c9a3c8", "node_type": "4", "metadata": {"page_label": "6", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "a4a45516b0cea3620e23e959aa75952818c54664940d9ab2f799288edab2ce94", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-2 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nADM EHS Municipality of Abu Dhabi City - Guide Lines for Developing EHS Plan for \nBuilding and Construction Sector; \nADQCC (TR-516) Abu Dhabi Road Structures Design Manual; \nBS British Standards; \nBS EN European Standards; \nDOT-M-08 Environmental, Health and Safety Manual for Road Projects; \nOSHA Occupational Safety and Health Administration. \nTable 2-1 and Table 2-2 presents American Association of State Highway and Transportation \nOfficials (AASHTO), American Society for Testing and Materials (ASTM), British (BS), and European \n(BS EN) Standards that are related to materials for earthworks. It also includes designations and \ntitles. \nTable 2-1: Designations and titles for AASHTO and ASTM standards that apply to \nearthworks \nAASHTO \nDesignation ASTM Designation Title \nASTM E380 - 2010 Standard Practice for Use of the International System \nof Units (SI) (the Modernized Metric System).  \nASTM D1452 - 09 Standard Practice for Soil Exploration and Sampling by \nAuger Borings.  \nASTM D2113 - 08 Standard Practice for Rock Core Drilling and Sampling \nof Rock for Site Investigation.  \nASTM D4647 / \nD4647M - 13 \nStandard Test Methods for Identification and \nClassification of Dispersive Clay Soils by the Pinhole \nTest \nASTM D4542 - 07 \nStandard Test Method for Pore Water Extraction and \nDetermination of the Soluble Salt Content of Soils by \nRefractometer.  \nASTM D2974 - 07a Standard Test Methods for Moisture, Ash, and Organic \nMatter of Peat and Other Organic Soils.  \nASTM D5333-03 Standard Test Method for Measurement of Collapse \nPotential of Soils (Withdrawn 2012). \nAASHTO M 145 Standard Specification for Classification of Soils and \nSoil-Aggregate Mixtures for Highway Construction \nPurposes.  \nASTM D5731 - 08 Standard Test Method for Determination of the Point \nLoad Strength Index of Rock and Application to Rock \nStrength Classifications.  \nAASHTO T 176 Standard Method of Test for Plastic Fines in Graded \nAggregates and Soils by Use of the Sand Equivalent \nTest. \nAASHTO T 96 Standard Method of Test for Resistance to Degradation \nof Small-Size Coarse Aggregate by Abrasion and \nImpact in the Los Angeles Machine.  \nAASHTO T 27 Sieve Analysis of Fine and Coarse Aggregate.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 2310, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d9ea4a07-354b-41b4-b414-752c92ea04d0": {"__data__": {"id_": "d9ea4a07-354b-41b4-b414-752c92ea04d0", "embedding": null, "metadata": {"page_label": "7", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "d9eae386-5f7d-49d3-a479-5f1d1fccc254", "node_type": "4", "metadata": {"page_label": "7", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "0e57d732144de99bd9526835e682af36e49a0c530488fe34f5fecc1852435b03", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-3 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nAASHTO \nDesignation ASTM Designation Title \nAASHTO T 11  Standard Method of Test for Materials Finer Than 75-\nµm (No. 200) Sieve in Mineral Aggregates by Washing.  \nAASHTO M 288  Standard Specification for Geotextile Specification for \nHighway Applications.  \n ASTM D5261 - 10 Standard Test Method for Measuring Mass per Unit \nArea of Geotextiles. \n ASTM D4632 - 08 Standard Test Method for Grab Breaking Load and \nElongation of Geotextiles.  \n ASTM D6241 - \n04(2009) \nStandard Test Method for the Static Puncture Strength \nof Geotextiles and Geotextile-Related Products Using a \n50-mm Probe.  \n ASTM D4533 - 11 Standard Test Method for Trapezoid Tearing Strength \nof Geotextiles.  \n ASTM D4355 - 07 Standard Test Method for Deterioration of Geotextiles \nby Exposure to Light, Moisture and Heat in a Xenon \nArc Type Apparatus. \n ASTM D4751 - 04 Standard Test Method for Determining Apparent \nOpening Size of a Geotextile.  \n ASTM D4491 - \n99a(2009) \nStandard Test Methods for Water Permeability of \nGeotextiles by Permittivity. \n ASTM D5199 - 12 Standard Test Method for Measuring the Nominal \nThickness of Geosynthetics. \nAASHTO T 180  Standard Method of Test for Moisture-Density \nRelations of Soils Using a 4.54-kg (10-lb) Rammer and \na 457-mm (18-in.) Drop.  \nAASHTO T 99  Standard Method of Test for Moisture-Density \nRelations of Soils Using a 2.5-kg (5.5-lb) Rammer and \na 305-mm (12-in.) Drop. \nAASHTO T238  Standard Method of Test for Density of Soil and Soil-\nAggregate In-Place by Nuclear Methods (Shallow \nDepth). \nAASHTO T239  Standard Method of Test for Moisture Content of Soil \nand Soil-Aggregate In-Place by Nuclear Methods \n(Shallow Depth). \nAASHTO T 2  Standard Method of Test for Sampling of Aggregates. \nAASHTO T86  Investigations and Sampling Soils and Rock for \nEngineering Purposes.  \nAASHTO T 87  Standard Method of Test for Dry Preparation of \nDisturbed Soil and Soil Aggregate Samples for Test.  \nAASHTO T 88  Standard Method of Test for Particle Size Analysis of \nSoils.  \nAASHTO T 89  Standard Method of Test for Determining the Liquid \nLimit of Soils.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 2200, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "41455b7e-5d66-4cfe-a037-ac33ac210cb9": {"__data__": {"id_": "41455b7e-5d66-4cfe-a037-ac33ac210cb9", "embedding": null, "metadata": {"page_label": "8", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "6e50b4db-a758-4431-b1a4-2e74cbc5fc72", "node_type": "4", "metadata": {"page_label": "8", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "645788a955474cc0d5bbc29267a58e8d02a03e90804a4036d6253ef44c01339d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-4 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nAASHTO \nDesignation ASTM Designation Title \nAASHTO T 90  Standard Method of Test for Determining the Plastic \nLimit and Plasticity Index of Soils.  \nAASHTO T 93  Standard Specification for Determining the Field \nMoisture Equivalent of Soils.  \nAASHTO T 217  Standard Method of Test for Determination of Moisture \nin Soils by Means of a Calcium Carbide Gas Pressure \nMoisture Tester. \nAASHTO T 100  Standard Method of Test for Specific Gravity of Soils. \nAASHTO T 85  Standard Method of Test for Specific Gravity and \nAbsorption of Coarse Aggregate. \nAASHTO T 193  Standard Method of Test for The California Bearing \nRatio.  \nAASHTO T 191  Standard Method of Test for Density of Soil In-Place by \nthe Sand-Cone Method.  \nAASHTO T 204  Standard Method of Test for Density of Soil In-Place by \nthe Drive Cylinder Method.  \nAASHTO T 205  Standard Method of Test for Density of Soil In-Place by \nthe Rubber-Balloon Method.  \n ASTM D4873 – 02 \n(2009) \nStandard Guide for Identification, Storage, and \nHandling of Geosynthetic Rolls and Samples \n ASTM D4943 - 08 Standard Test Method for Shrinkage Factors of Soils \nby the Wax Method.  \nTable 2-2: Designations and titles for BS and BS EN standards that apply to earthworks \nBS \nDesignation \nBS EN \nDesignation \nTitle \nBS 1377-4  Methods of test for soils for civil engineering purposes - part 4: \nCompaction-related tests. \n2.1.3 Contractor's Responsibility \n2.1.3.1 Site Visit \nContractor shall be deemed to have visited the site prior to submitting his Tender and to have made \nall necessary inspections and investigations per the requirements contained in Section  1.6.1 of \nChapter 1, General Requirements of these Standard Specifications. \n2.1.3.2 Boreholes   \nContractor shall perform all work as required to auger or core drill boreholes or test borings when \ndirected by the Engineer when unsuitable soils are encountered at the bottom of excavations or \nwhere directed by the Engineer within or immediately adjacent to the work area \nThe boreholes drilling, sampling and logging shall be carried out in full compliance with ADQCC \nManual for Geotechnical Works (TR-509 Vol. 1 and 2).", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 2255, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d2b1e02f-7084-45ad-9840-9775c31c2add": {"__data__": {"id_": "d2b1e02f-7084-45ad-9840-9775c31c2add", "embedding": null, "metadata": {"page_label": "9", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "61ac8878-6b6f-4039-8b3c-5557469478fb", "node_type": "4", "metadata": {"page_label": "9", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "86c216362283ad1d1cf76392bceb6be709414135ea6df0f1d7c8b90a0803f9f2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-5 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nPurpose of these boreholes shall be to determine the character, thickness and stratification of the \nsubsurface material, the relative density and permea bility of granular materials, coring of rock and \nsuch other data as may be required by the Engineer to determine the adequacy of the subsurface \nmaterial. \na. <PERSON> of Boreholes\nDepth of such boreholes and the depth at which core samples shall be taken shall be as directed by \nthe Engineer.  \nThe depth of the boreholes shall be determined according to the type and characteristics of the \nsubsurface soil. If no rock is encountered, bo reholes shall be driven to a depth of 20 metres in \npredominantly gravelly-sandy-silty soils and to 30 metres in soft clay soils or soils with a low bearing \ncapacity where piling may be required, all in accordance with the directions and to the satisfaction  \nof the Engineer. If rock is encountered before reaching the above depths, the borehole shall \npenetrate at least 10 metres in the rock. If the thickness of the rock is less than 10 metres, the \nborehole shall be continued as specified above and/or as directed by the Engineer. \nb. Drilling of Boreholes\nBoreholes or test borings shall be advanced between sampling intervals by driving a steel casing to \nsuch depth below the ground surface. Casing diameter shall be as required to facilitate removal of  \nthe soil sampling or rock coring that is required. \nBoreholes shall be augered or core drilled as directed by the Engineer. Augered boreholes shall \ncomply with the requirements of ASTM D1452 and as specified herein. Core drilled boreholes shall \ncomply with ASTM D2113 with double tube, swivel -type, M-design core barrels and as specified \nherein.  Casing at boreholes shall be NX size, with outside diameter of 89 mm and inside diameter \nof 76.2 mm. \nThe boring shall be performed with continuous coring and the cores extracted shall be carefully kept \nin proper core boxes so that the lithology can be recorded as accurately as possible. A preliminary \ndescription of the core shall be made when it is extracted. Then these descriptions, together with \ntechnical operational details shall be entered in the daily log which shall be available for inspection \nby the Engineer at all times. \nc. Borehole Equipment and Methods\nMethod and the equipment to be used for advancing the borehole shall be submitted by the \nContractor to the Engineer for approval. Casing shall be driven without the use of wash water unless \nthe Engineer specifically approves simultaneous washing and driving.  Washing, spudding or drilling \nahead of the casing will not be permitted. \nBetween the depth  intervals at which sampling is accomplished as hereinafter specified, loose \nmaterial within the casing shall be removed by the usual wash pipe method or other suitable means. \nNo downward directed jets will be permitted.  Casing shall remain in the hole u ntil the Engineer \nauthorizes its removal. \nTo prevent caving and mixing strata, casing or drilling mud material (bentonite) shall be used when \nboring through any stratus which is not sufficiently cohesive to stand firmly without it. \nUse of drilling mud to stabilize a borehole will be permitted only if the Engineer deems it is not \ndetrimental to pumping tests or groundwater observations. \nd. In-Situ Testing and Sampling\nStandard Penetration Tests (S.P.T.) is to be performed during the course of the boring at 1.0m \nintervals or at every change of stratum.  \nPocket penetrometer and/or vane test measurements shall be performed on all suitable cohesive \nsamples immediately after their sampling and before waxing. \nDisturbed samples shall be take n from the top of each stratum from the material contained within \nthe standard penetration tool after the S.P.T. has been carried out, and from the cutting shoe of the", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3932, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "999b634a-3943-45f5-9de2-84c80346b14f": {"__data__": {"id_": "999b634a-3943-45f5-9de2-84c80346b14f", "embedding": null, "metadata": {"page_label": "10", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "4adf43d7-2a09-48df-a35d-5aad35dd8e00", "node_type": "4", "metadata": {"page_label": "10", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "1dfacb70b2f635a211ba67b77de2ca8e305b44b178bb98f4e242c0020e79e01c", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-6 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nundisturbed sampler after the undisturbed samples have been taken.  Each soil samples sha ll be \nstores in a container of glass, plastic or other approved material, fitted with an airtight cover and \nwaxed immediately being taken from the boreholes. Each container shall be numbered and labelled \nin such a way that the sample can be easily be ident ified with the boreholes, the soil stratum, the \ndepth from which it was taken and the date on which it was taken  \nUndisturbed samples should be taken in cohesive soils including mixed soils. Undisturbed samples \nshall be taken from the most significant beds using appropriate means. The internal diameter of the \nsampler shall be about 10cm and the useful length at least 50cm. The samplers shall be in good \ncondition and therefore shall be no signs of oxidation on the inner walls or damage to the base.  \nAfter the samples have been taken the ends shall be coated with wax, after having removed the \nloose material. The containers shall then be sealed and clearly marked as specified for the disturbed \nsamples. A careful description shall be made of the undisturbed sam ples in the laboratory when \nthey are extracted from the container and before starting the required laboratory testing. \nSamples and rock cores, immediately upon recovery, shall be placed in containers as approved by \nthe Engineer, identified as specified her ein and delivered to the Site Laboratory included under \nSection 1.19.7 of Chapter 1, General Requirement, of these Standard Specifications. \nEach sample or rock core shall be identified and include the following information: \n1. Location of borehole.\n2. Date when sample was taken.\n3. Boring number.\n4. Surface elevation.\n5. Name of driller.\n6. Description and length of casing, if any.\n7. Depth of bottom of boring.\n8. Depth from which the sample or rock core was taken.\nTesting of all samples and rock cores will be performed by th e Engineer in the Site Laboratories. \nContractor shall furnish the Engineer with all pertinent data as may be required relative to the sample \nso as to aid the Engineer in his testing program. \ne. Water Levels\nAll water levels encountered in the boreholes shall be recorded and a check shall be made of the \nstatic level after boring has been completed. \nf. Borehole Logs\nBoreholes logs shall be supplied to the Engineer as soon as possible after the borings have been \ncompleted. They shall be in such a fo rm as to indicate the location relative to any of the reference \npoints, the description and thickness of each type of soil, the level of the lower contact of each \nstratum relative to the said level, the level of the water table if any, and the position and  record \nnumber of every sample taken. The records are to be written in English and the descriptions of the \nlayers have to be in accordance with the International Standards. Furthermore, the records shall \ninclude the results of standard penetration tests and of pocket penetrometer and/or vane tests and \nrock core descriptions (total core recovery, solid core recovery and rock quality designation).  \nThe site investigation works shall not be regarded as complete until all boreholes logs have been \nsubmitted to the Engineer and approved by him. \ng. Laboratory Tests\nAs a minimum, the following laboratory tests are to be carried out: \ni. For disturbed samples:", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3465, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8007dc05-096c-4966-878a-a60341eceaa0": {"__data__": {"id_": "8007dc05-096c-4966-878a-a60341eceaa0", "embedding": null, "metadata": {"page_label": "11", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "a13ce568-687d-4fc0-b2bd-03c2dad7e14e", "node_type": "4", "metadata": {"page_label": "11", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "89dcd98d0119cd628ac8244441eab4832d941cd2e9cbaf8b841ac849100a1c4a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-7 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n• Grading and Atterberg Limits (LL and PL) on repres entative samples from each\nborehole as requested by the Engineer. \nii. For undisturbed samples:\n• Grading by sieves and or by sedimentation,  Atterberg Limits (LL and PL), Natural\nMoisture Content and Bulk Density on all samples. \n• Consolidated undrained triaxial test (3 tests at different confining pressures on three\n1-1/2\" diameter specimens from each sample) on samples selected by the Engineer.\n• Oedometer consolidation tests on samples selected by the Engineer.\niii. Unconfined compression tests on rock samples.\niv. Corrosion effect on concrete of water and soils.\nThe results of the tests shall be given to the Engineer on approved forms. All measurements shall \nbe expressed in the decimal system.  \nThe above laboratory tests provide a general guideline. Additional testing or elimination of some \ntests may be requested by the Engineer \nh. Final Reports\nOn completion of all laboratory testing a final report in triplicate shall be submitted to the Engineer. \nThis final report shall include a g eneralized subsoil profile and all borehole logs, soil identification, \nin-situ and laboratory testing, observations and recommendations on the type of foundation and on \nsafe bearing capacity of the foundation soils and where piles are advised recommendations on their \nsafe bearing capacity. \n2.2 Clearing and Grubbing \n2.2.1 Description \nThis section describes clearing of the site, grubbing, and removal of topsoil necessary for \nconstruction of the work covered by the Contract, in accordance with these specifications. \n2.2.2 Construction Requirements \nNo clearing and grubbing shall be performed unless approved by the Engineer. Contractor shall take \nall necessary precautions to prevent damage to structures and other private or public property. \nAny unauthorised damage to, or interference with, private or public property, including trees, shall \nbe repaired to the satisfaction of the Engineer and the owner, at the Contractor’s sole expense. \n2.2.2.1 Areas to be Cleared, Grubbed, and Removed of Topsoil \nAreas to be cleared, grubbed, and removed of topsoil shall be in accordance with the particular \nspecifications and Contract plans, or as directed by the Engineer. In general, clearing and grubbing \nshall be performed to 3 m outside the top of the fill slope and 3 m outside the top of the cut slope, to \ninclude the following areas: \n1. To be excavated, including areas staked for slope treatment\n2. Where utility, drainage, and subdrainage trenches shall be dug, unsuitable material\nremoved, or structures built \n3. Upon which embankments shall be placed\n4. Wherever road, parking, and pedestrian pavement is to be placed\nNo trees that fall between the worksite and the limits of clearing and grubbing shall be removed or \ndamaged without the written authorisation of the concerned authority. Contractor shall submit shop \ndrawings, depicting the existing trees within the limits of the woks, clearly indicating those to be \nremoved and those to be retained, for the Engineer’s approval and as per the tree removal \nmanagement plan as may be contained in the Contract Documents. Individual trees designated by \nthe Engineer shall be left standing and uninjured.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3355, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4f31f787-9d6c-43db-8c68-d8c6962b587a": {"__data__": {"id_": "4f31f787-9d6c-43db-8c68-d8c6962b587a", "embedding": null, "metadata": {"page_label": "12", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "327f3902-62b6-4229-b672-d35ad99911a2", "node_type": "4", "metadata": {"page_label": "12", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "fe814b0f9a9ba51a686decba0fe2f74cd9ce6a0ebc5b49b7947d2fc447c93ac9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-8 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nWithin built-up areas, clearing and grubbing shall be strictly limited to the extent of cut or fill as shown \nin the Contract plans or as directed by the Engineer. No private property such as buildings, crops, \nsigns, fences, etc., shall be removed, relocated, or altered without the authorisation of the Engineer. \n2.2.2.2 Clearing \nClearing shall consist of the removal of all trees (only as approved by the Engineer), bush, shrubs, \nother vegetation, rubbish, fences, and all other objectionable material, including the disposal of all \nmaterial resulting from clearing and grubbing. Clearing shall also include the removal of all rocks and \nboulders that are exposed or lying on the surface. \n2.2.2.3 Grubbing \nIn any roadway and paved area, all stumps and roots exceeding 50 mm in diameter shall be removed \nat least to the depth that is the larger of the following: \n 600 mm below the finished pavement level \n 100 mm below the ground level after removal of topsoil \n3. 300 mm below the top of improved subgrade or formation level \nAll stumps and roots, including matted roots, shall be removed to a depth of at least 300 mm below \nthe formation level — the top of the improved subgrade. Cavities resulting from the grubbing shall \nbe backfilled with approved material and compacted to comply with the specifications for the relevant \nlayer. \n2.2.2.4 Conservation of Existing Topsoil, Vegetation, and Cutting of \nTrees \nWhere provided for in the Contract plans, certain designated areas of existing topsoil, vegetation, \nand trees encountered in the road reserve shall be carefully protected by the Contractor. Contractor \nshall provide temporary fencing around these areas and shall provide instruction to all personnel and \nequipment operators not to intrude within that fencing. Protective fencing shall meet the requirements \nof Section 1.21.2 of Chapter 1, General Requirements, of these Standard Specifications. \nWhere the Contract Plans  or particular specifications include specific plants to be protected or \nremoved and replanted, the Contractor sh all include in its bid rates for clearing and grubbing full \ncompensation for omitting or the careful removal and planting of the plants in a protected and fenced-\noff area. Additionally, on completion of the road, the Contractor shall provide for replanting  of the \nplants in suitable positions in accordance with the Engineer’s instructions. \nIn cases where the Engineer has approved the cutting of trees, they shall be cut in sections from the \ntop downwards. Branches of trees to be left standing shall be trimmed so as to leave a 7-m clearance \nabove the carriageway and parking areas, and a 2.5 -m clearance above pedestrian walkways.  \nUnless otherwise approved by the Engineer, the Contractor shall engage an Owner approved \nsubcontractor for the trimming, removal and replanting of the trees. \n******* Removal and Salvage of Topsoil \nTopsoil shall be removed to a depth as instructed by the Engineer within the areas requiring clearing \nand grubbing. \nIf not used immediately, the topsoil shall be transported and deposited in stockpiles or spoil banks \nprovided by the Contractor at locations approved by the Engineer. Salvaged topsoil shall be reused \nfor agricultural and landscape areas. \n2.2.2.6 Roadside Cleanup \nRoadside cleanup, as ordered by the En gineer, consists of work not otherwise provided for in the \nContract. Such work may include:", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3548, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4fbbcdfc-bb7c-451b-81e3-21f9353b88cc": {"__data__": {"id_": "4fbbcdfc-bb7c-451b-81e3-21f9353b88cc", "embedding": null, "metadata": {"page_label": "13", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "9624ec0a-d0fb-44af-b755-1304ee3c73ee", "node_type": "4", "metadata": {"page_label": "13", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "47055333309f3275d391987673ffc01004275822a7cc02603532a4350d11d6b2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-9 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n1. Removing trees, snags, brush, other vegetation, upturned stumps, large rocks and\nboulders, debris, and other unsightly matter outside the areas staked for clearing or \ngrubbing within  project right-of-way \n2. Trimming trees or brush\n3. Filling holes and smoothing and contouring the ground\n4. Shaping the ends of cuts and fills to fit adjacent terrain and to enhance the area’s\nappearance \n5. Obliterating abandoned roads and reshaping the areas to blend naturally with\nsurroundings within the project right-of-way \nMethods and equipment used in roadside cleanup shall be approved by the Engineer. \n2.2.2.7 Disposal of Material \nMaterial obtained from clearing, grubbing, and removal of topsoil shall be disposed of as approved \nby the Engineer. Materials to be disposed of shall generally be hauled to a waste site obtained and \nprovided by the Contractor or placed in borrow pits or other suitable places and covered up with soil \nor gravel, with full coordination and approvals from the concerned authorities.  Trees and brush to \nbe removed may be chipped in place.  Topsoil to be reused, shall be stockpiled. \nBurning of material shall only be permitted by prior written appr oval of the Engineer. All statutory \nprovisions with regards to environmental constraint and air pollution shall be carefully observed. \n2.3 Removal of Structures and Obstructions \n2.3.1 Description \nThis section describes the removal and d isposal of structures that obtrude, encroach upon, or \notherwise obstruct the work. \n2.3.2 Construction Requirements \n2.3.2.1 General \nContractor shall remove and dispose of all buildings, foundations, bridges, drainage structures, \npavement, railings, barriers, fences and other obstructions within the limits of the work, except items \ndesignated to remain and utilities and obstructions to be removed under other provisions of the \nContract. \nAll designated salvage material shall be removed, without damage, in sections or pieces that may \nbe readily transported, and shall be stored by the Contractor or transported to the Owner warehouse \nby the Contractor, as directed by the Engineer. \nBasements or cavities left by structure removal shall be filled to the level of the surrounding ground \nand shall be compacted in accordance with the relevant requirements in this specification. \nExisting utilities running through or near existing structures shall be protected and/or relocated in \naccordance with the Contractor’s coordination with the appropriate utility authority. \n2.3.2.2 Removal of Structures and Foundations \nDemolition, removal, and disposal of all buildings and structures designated for removal on the \nContract plans or in the particular specifications shall be completed by the Contractor. Work shall \ninclude the removal and disposal of all excess debris and th e removal of all other foundations, \npaving, concrete floor slabs, sidewalks, signs, sheds, garages, fences, and tanks, as well as any \nother miscellaneous work necessary to fully complete the removal of the buildings and \nappurtenances.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3160, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d2907336-9ca0-4eab-b643-0efba4c349a3": {"__data__": {"id_": "d2907336-9ca0-4eab-b643-0efba4c349a3", "embedding": null, "metadata": {"page_label": "14", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "7b3730fa-fb3e-4721-9760-f2d2f9fddddc", "node_type": "4", "metadata": {"page_label": "14", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "13e832b957684c2c42fa621821be941b48e07b3e391a9d08437e8267209952b5", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-10 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nDiscontinuance of all  utility services that serve the building or structure shall be arranged by the \nContractor, who shall also properly remove, disconnect, and plug all such services in accordance \nwith the requirements of the respective utility agency. \nExisting underground storage tanks on-site or within the buildings shall be removed and disposed of \nby the Contractor, who shall take all necessary precautions to remove any remaining fluids within \nthe tanks. Any hazardous materials and petroleum products shall be handled and di sposed of in \naccordance with Section 2.3.2.5.  When removing foundations, the Contractor shall perform the \nfollowing:  \n1. Remove foundations to a dep th of at least 300 mm below finished ground elevation or \nsubgrade elevation, whichever is lower. \n2. Break up basement floors to promote drainage. \n3. Fill basements or other cavities left by the removal of structures. Fill shall match the level \nof the surrounding ground. Fill within the area of the roadway embankment prism, under \npedestrian pavement, or adjacent to other structures and utilities that require lateral or \nbearing support. Fill shall be compacted to meet the requirements of Section *******. \n2.3.2.3 Removal of Bridges, Culverts, and Drainage Structures \nBridges, culverts, and other drainage structures shall not be removed until the Contractor has made \nsatisfactory arrangements to accommodate traffic and the flow of water. Contractor shall not \ncommence removal work before preparing and submittal and the Engineers approval of a Removal \nMethod Statement which provides a detailed description of the Contractor ’s proposed removal \nmethods, public and traffic safety accomodations, noise and dust controls, disposal methods and \nlocations, and any other item as required by the Engineer. \nUnless otherwise indicated, the existing substructures shall be removed 300 mm be low the natural \nground surface. Portions of existing structures within the limits of a new structure shall be removed \nto accommodate the construction of the proposed structure. \nWork includes removal and disposal of underground obstructions, including walls ; individual rocks; \nstructures; abandoned drainage structures, pipe lines, and utilities; foundations; and other structures \nof steel or concrete. \nRemoval of existing utilities required to permit the orderly progression of work shall be accomplished \nin coordination with the appropriate agency. Contractor shall contact the proper agency or owner \nand arrange for its removal. \nBridges designated as salvaged material shall be dismantled without damage and match -marked. \nStructures designated to become the property of the Contractor shall be removed from the site. \nBlasting or other operations necessary for removing existing structures or obstructions that may \ndamage new construction shall be completed by the Contractor prior to placing new work. Use of \nexplosives shall be governed by Section *******. \nWhere partial demolition of obstructions or structures is indicated, the Contractor shall remove the \nindicated structure with care and caution so as not to damage the remaining portions of the existing \nstructure. \n2.3.2.4 Removal of Pavement, Barriers, Railings, Sidewalks, Kerbs, \nand Gutters \nExisting pavement shall be removed to the subgrade level at locations identified in the Contract plans \nor the particular specifications. Where existing pavement is removed along a section of pavement to \nremain, the Contractor shall saw-cut to neat lines along the break line in such a manner as to avoid \ndamage to the pavement that is to remain. Any damage to pavement areas that is to rema in shall \nbe restored to the satisfaction of the Engineer and in accordance with Section 3.4.3.2 of Chapter 3, \nPavement, of these Standard Specifications.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3935, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "51ce4602-bdda-4da5-8416-29d3358d54bb": {"__data__": {"id_": "51ce4602-bdda-4da5-8416-29d3358d54bb", "embedding": null, "metadata": {"page_label": "15", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "450f1d04-7baa-4d39-a638-4a709ea085dd", "node_type": "4", "metadata": {"page_label": "15", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "d4dfce89ca45096a4fa5f8b458e5e13d305ba218acda9d693b4edc434344fe49", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-11 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nExisting asphalt pavement shall be broken up and stockpiled for reuse in aggregate -based coarse \nlayers as stipulated in Section 3.2.2 of Chapter 3, Pavement, of these Standard Specifications. \nAfter removal of pavements, the old roadbed shall be scarified or ploughed to mix the remaining road \nmaterial thoroughly with earth and shall be graded and smoothed to a pleasing appearance to match \nand blend in with the existing contours and terrain. \nWhere barriers, railings, kerbs and gutters are to be removed adjacent to pavement to remain, the \nwork shall be carried out without damaging the existing pavement. Haunching and foundations shall \nbe included in the removal. Removed railings kerbs, sidewalks, gutters, and barriers shall be properly \ndisposed of, and shall not be reused on the project. \n2.3.2.5 Disposal of Materials \nAs much as practicable, waste concrete, asphalt, and masonry shall be broken into suitably sized \npieces and incorporated in embankment slopes or basement fills. Any broken concrete or masonry \nand all other materials not considered suitable for use in construction or elsewhere shall be disposed \nof by the Contractor at approved locations. In no case shall any discarded materials be left adjacent \nto or within the site. \nEngineer will approve the manner in which materials are disposed and their location, which shall not \ncreate an unpleasant or objectionable view. When the Contractor requires a disposal area outside \nof the jobsite, the Contractor shall obtain permission in writing from the appropriate agency and \nwaste disposal operation, and file this information with the Engineer. \nIf the Contractor or Engineer finds any evidence that excavated or waste material has become \ncontaminated with petroleum products or other hazardous materials, as determined by visual, \nolfactory, or other means, the potentially contaminated materia l shall be stockpiled separately, \nsampled, and analysed to determine disposal options. \n2.4 Roadway Excavation \n2.4.1 Description \nExcavation consists of the following: \n1. Roadway excavation  consists of all  materials excavated from within the right -of-way or \neasement areas, (including all material encountered regardless of its nature or \ncharacteristics) except sub -excavation described in 2 below and structure excavation \ndescribed in 4 below. \n2. Subexcavation consists of material excavated from below subgrade elevations in cut sections \nor from below the original ground line in embankment sections. Subexcavation does not \ninclude the work required in Section *******. \n3. Borrow excavation includes the material used for embankment and other fill construction that \nis obtained from outside of the roadway prism. \n4. Structural excavation consists of material excavated for purposes of structure construction. \nWork described in this section, regardless of the nature or type of the materials encountered, \nincludes excavating and grading the roadway, excavating in borrow pits (sites providing fill \nmaterials), excavating below-grade, excavating channels, excavating and grading non-load bearing \nareas outside of the traffic and pedestrian pavement areas, removing slide material, and disposing \nof all excavated material. These activities may be performed in making cuts, embankments, slopes, \nroadway ditches, approaches, parking areas, intersections, and pedestrian walkways, and in \ncompleting related work.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3535, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "71806e9f-825d-4ced-967f-3243bd8cbbe5": {"__data__": {"id_": "71806e9f-825d-4ced-967f-3243bd8cbbe5", "embedding": null, "metadata": {"page_label": "16", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "9010b59d-6d1a-40f8-a336-82bf3ea93e5b", "node_type": "4", "metadata": {"page_label": "16", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "5edec4120cb9f57a89482ae4974263c5195dfc8de2a15c4972b9fcaa1929fbc9", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-12 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n2.4.2 Construction Requirements \n2.4.2.1 Preparation for Roadway Excavation \nCross-sections and topographic information shall be surveyed, plotted and submitted in accordance \nwith Section 2.1, prior to the start of excavation and other earthwork. \nAreas of vegetation and obstructions shall be cleared in accordance with Sections 2.2 and 2.3. \nTopsoil shall be conserved from roadway excavation and embankment foundation areas in \naccordance with Section *******. \nAt the end of each day's operations during the rainy season, the work area surface shall be shaped \nto drain to a uniform cross-section. All ruts and low spots that could hold water shall be eliminated. \n2.4.2.2 Control of Excavation \nAll roadway excavation shall be finished to a reasonably smooth uniform surface, shall not vary by \nmore than 15 mm above or below the grade established, and shall be in reasonably close \nconformance to the lines, dimensions, and cross -sections shown on the Contract plans  or as \nestablished by the Engineer. \nInstructions from the Engineer shall be obtained by the Contractor before starting the work in regards \nto the slope of excavation sides slopes and the depth to take cuttings, including the dimensions of \nany in-situ treatment of cuts that may be required below the formation level. Excavation in soils shall \nbe trimmed and graded to a smooth finish to prevent initiation of slope erosion. \nWhen excavating, the Contractor shall take proper care not to loosen any m aterial outside of the \nspecified cut line, whether by ripping, blasting, or by other means. Care shall also be taken not to \nundercut any slopes, and proper control shall be exercised by regular survey checking and by using \nbatter poles at close intervals at all times. \nAll excavations made outside the specified cut line, or below the specified level, without the approval \nof the Engineer, shall be backfilled with approved material, compacted to the satisfaction of the \nEngineer, and re-trimmed; all at the Contractor's expense. \na. Direct Hauling\nIf it is practical, the Contractor shall haul roadway or borrow fill materials immediately from the \nexcavation site to its final place on the roadbed. Payment for such work shall be made in accordance \nwith the bill of quantities item for roadway excavation. \nb. Delayed Excavation\nIf it is impractical to haul roadway or borrow fill material to its final place at once, the Contractor shall \ndelay excavation until the placement is workable. No extra payment shall be made for delayed \nexcavation. \nc. Stockpiling\nIf delaying the excavation will hamper grading or force impractical movements of equipment, the \nEngineer may allow the Contractor to stockpile roadway or borrow fill materials. In this case, the \nEngineer will approve where and when the Contractor may excavate, stockpile, haul, and place the \nfill materials. No additional payment will be made for stockpiling roadway or borrow fill material. \n2.4.2.3 Dewatering \na. Description and General Requirements\nDewatering operations shall comply with the following requirements, unless otherwise approved by \nthe Engineer. \n1. Contractor shall furnish, install, operate and maintain all equipment and appliances\nnecessary to keep excavations free from water at all times during construction.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3399, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "909a2b85-8707-4c86-a23f-309cd3d65ef6": {"__data__": {"id_": "909a2b85-8707-4c86-a23f-309cd3d65ef6", "embedding": null, "metadata": {"page_label": "17", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "a99437ac-950d-4aad-b8b9-c9a52f997299", "node_type": "4", "metadata": {"page_label": "17", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "a217d4fae42149ad0cd86aed861351f8edd122c458073acf6b6094f48532f4f0", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-13 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n2. Contractor must also provide sufficient backup equipment (including pumps) in order to\nsustain the dewatering systems at all times \n3. Water from other construction activities shall be prevented from entering an excavation by\nsuitable means. \n4. All water must be removed in a manner to avoid damage to property and minimise nuisance\nor menace to the public \n5. Contractor shall be responsible for any structures subjected to hydrostatic pressure from\nground water during construction \n6. In general,  all dewatering sh all be performed by well -point systems, unless otherwise\napproved by the Engineer \n7. Contractor shall ensure that the static water level will be drawn down to a depth sufficient to\nkeep the bottom of the excavation dry (at least 300 mm below formation level) \n8. No excavation shall be allowed in wet conditions and no pipelaying or concrete activities shall\ncommence until the Contractor demonstrates that he can maintain the excavations in dry \nconditions, acceptable to the Engineer \n9. Contractor must maintain dewatering at all times during construction so that no groundwater\ncomes into contact with any reinforcement or unprotected concrete surfaces \n10. All dewatering discharge shall have suitable sediment traps, filters or settling tanks to avoid\ndischarge of any sands and sediments where discharging to an existing storm drain, ditch or \narea that has be en declared a sensitive or protected area by the environmental agency or \nthe Engineer  \n11. Failure to comply with the above shall entitle the Engineer to condemn the section of work\naffected and demand complete removal and replacement, at the Contractor’s expense. \nb. Qualified Personnel\nContractor must provide experienced, qualified personnel to perform the dewatering operations \nIf approved by the Engineer, the Contractor may furnish the services of an experienced, qualified \nand properly equipped dewatering Subcontractor to design and operate the dewatering and \ngroundwater recharging systems required for the work. \nc. Monitoring\nWhere required to do so by the Engineer, the Contractor shall establish a specified number of \ngroundwater level monitoring stations at each site, which will be observed during the work.  These \nshall be located as directed by the Engineer and consist of acceptable open tube piezometers \nIf required by the Engineer, settlement gauges shall be provided as designated by the Engineer, to \nmonitor settlement existing structures and facilities \nd. Construction Requirements\n1. Well point systems:  All well -point systems shall conform to the following requirements,\nunless otherwise approved by the Engineer: \na. Well-point system shall be placed on bot h sides of a trench or completely around\nisolated excavations \nb. Maximum allowable drawdown for a single stage system shall be 4.0 meters\nc. Multi-stage well-point systems shall be used for excavations requiring a draw -down\nof more than 4 meters \nd. Well-points at all levels of multi -stage systems shall be installed on both sides of a\ntrench or completely around isolated excavations \ne. Maximum spacing of well-points shall be 1.0 m", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3240, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f069fdc4-b5d7-4efa-ad8f-6d0d478669e6": {"__data__": {"id_": "f069fdc4-b5d7-4efa-ad8f-6d0d478669e6", "embedding": null, "metadata": {"page_label": "18", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "8e3d120d-68ed-450f-a3a2-f430922d0e14", "node_type": "4", "metadata": {"page_label": "18", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "07847f81ce34becbc106bdb2c9ecae86e64dea188ba6388aff683e89fe65eb75", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-14 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nf. Well-points shall be placed by jetting methods\ng. If necessary, auger boring shall be used in hard soils\nh. Well-points set in bored holes shall be backfilled with coarse sand\ni. All well-points shall have a suitable valve for “trimming” the system to obtain maximum\ndraw-down \nj. Vacuum assisted pumps of suitable size and quantity shall be used to maintain  the\nsystem \n2. Sump pumping:  Sump pumps may not be used, unless approved by the Engineer.  If\napproved, sump pumping shall comply with the following requirements: \na. At least two sumps shall be placed in each excavation (preferably in opposite corners)\nb. For long trenches, sumps shall be placed on each side of the trench\nc. Each sump shall be provided with a separate pump, suction hose and discharge line\nd. Sumps shall be large enough to hold sufficient water for priming, pumping and\nmaintaining the excavation floor in a relatively dry condition \ne. All sumps shall be fitted with a suitably sized cage for graded filter material\nf. Drains leading to the sump must be so arranged as to allow drainage of the entire\nexcavation and given sufficient fall to prevent silting up, unless steps are taken to \nkeep them cleared out \ng. Ditches shall be sufficiently wide to allow a water velocity low enough to prevent\nerosion \nh. Maximum water service elevation in the sump shall be at least 300mm below\nformation level \ni. Maximum draw down shall be 5-6 m\nj. For greater draw downs, use pumps at lower levels or use suspended submersible\npumps \nk. If excavation faces become unstable, the sump shall be abandoned and other\nmethods of dewatering shall be utilized \ne. Maintenance of Existing Water Table Level\nDewatering systems shall be installed and operated so that the groundwater level outside the \nexcavation is not reduced to the extent that would damage or endanger adjacent structures or \nproperty. \nIf necessary, a water injection recharging system shall be maintaine d to replenish the groundwater \nsupply as required to maintain the water table, including pumps, piping, well -points, standby units, \nother required equipment and a source of water sufficient to meet the recharge requirements, should \nsupply of water from dewatering operations be interrupted or become inadequate. \nContractor shall repair all damage or settlement to foundations, structures, existing facilities, or works \ndue to failure of excavation protection, operations of dewatering or recharging system, or fa ilure to \nmaintain the existing ground water outside the dewatering area.  \nContractor shall check discharge pipes at regular intervals, to ensure that the pumping system is \nfunctioning properly. \nf. Disposal of Water\nWater not injected back into the ground shal l be disposed of lawfully without damage to new and \nexisting facilities or adjoining properties.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 2935, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f757713e-1630-4126-a2b6-60511ae91723": {"__data__": {"id_": "f757713e-1630-4126-a2b6-60511ae91723", "embedding": null, "metadata": {"page_label": "19", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "6c3e05a0-4311-43af-8b46-ba7532917d6f", "node_type": "4", "metadata": {"page_label": "19", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "eb7b3a79b7e64af1bf5a6d6bb6eb6d185c815f742dc6cbc9be54ba4346c64ca2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-15 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nNo water shall be pumped into an existing or new drainage system, or to a environmentally sensitive \narea (as defined by the environmental authority or by the Engineer) unless discharge is equipped \nwith a suitable sediment trap, filter or sedimentation tank, as approved by the Engineer. \nWhere necessary, the Contractor shall divert natural and artificial waterways encountered at the site, \nuntil the works have been completed.  \nRoute of the dewatering discharge main shall be approved by the Engineer. \ng. Removal of Dewatering Systems\nNo dewatering system shall be removed without the approval of the Engineer.  Release of \ngroundwater back to its static level shall be performed in a manner to prevent disturbance of natural \nfoundation soils, compacted fill or backfill and to prevent flotation or movement of structures, \npipelines and sewers. \nEquipment shall only be removed when no longer required. Monitoring and settlement measurement \nsystems shall be maintained in operation as required by the Engineer. \nIf approved by the Engineer, well-points and like items may be abandoned in place. \nDrainage provisions: \n1. Drainage ditches, diversions and temporary pipes shall be constructed as  required to\nmaintain proper drainage in the work areas \n2. Drainage ditches shall be constructed with cross -sectional area and gradient at least equal\nto that of the intercepted watercourses or as approved by the Engineer \n3. Berms shall be provided to prevent surface water from draining into excavations\n4. Earth banks shall be suitably protected from erosion during excavation work.\n******* Rock Cuts \nContractor shall excavate rock cuts to 150  mm below subgrade level within the roadbed limits, \nbackfill to the subgrade with embankment materials that meet the requirements of Section  \n******* and compact the materials in accordance with Section 2.6. \nRock excavation using blasting shall meet the following requirements: \na. Rock Blasting\n1. Description\nRock blasting consists of fracturing rock and constructing stable final rock -cut faces using \ncontrolled and production blasting techniques. \nControlled blasting uses explosives to form a shear plane in t he rock along a specified back \nslope. Controlled blasting includes pre-splitting and cushion blasting. \nProduction blasting uses explosives to fracture rock. \nContractor shall be able to demonstrate suitable past experience and qualifications in the art of \nconstruction blasting.  Otherwise, a qualified Subcontractor shall be used.  Experience and \nqualifications, whether of the Contractor or a Subcontractor shall be submitted to the Engineer \nfor review and acceptance.  No blasting work shall be started until w ritten approval of the \nContractor’s qualifications and method statements have been approved by the Engineer. \n2. Materials\nExplosives and initiating devices less than one year old shall be used by the Contractor. These \ninclude, but are not limited to, dynamite  and other high explosives, slurries, water gels, \nemulsions, blasting agents, initiating explosives, detonators, and detonating cord.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3201, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "87272613-af6e-4abe-987c-ad71e3d10e79": {"__data__": {"id_": "87272613-af6e-4abe-987c-ad71e3d10e79", "embedding": null, "metadata": {"page_label": "20", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "2144aefc-b399-4ea0-8ec9-bb6324e8d1eb", "node_type": "4", "metadata": {"page_label": "20", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "9ab4e8ec575e88ff9da57c1dd6b64721d839a768fe947f6275a673c6e7cac504", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-16 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \n3. Construction Requirements \nI. Regulations. Furnish copies or other proof of all applicable permits and licenses that comply \nwith the requirements of the relevant government ministries. Upon Owner approval, the \nContractor shall comply with any additional requirements. \nII. Safety and health. Comply with the requirements of Section 1.16.7 of Chapter 1, General \nRequirements, and when required by the Engineer OSHA, Standard No. 1926, Subpart U. \nIII. Storage, security, and accountability.  Provide proper buildings or magazines in suitable \nlocations for storage of explosives, in a manner as approved by the Engineer. All storage \nshall be secure wit h locked access and guarded by security personnel 24 hours a day. \nProvide for a detailed method of controlling and inventorying of all explosive materials, as \napproved by the Engineer. All unused explosive materials shall be disposed of in a manner \napproved by the Engineer and the approving governmental Owner. \nIV. Blaster-in-charge. Designate, in writing, a blaster -in-charge, and submit evidence that the \nblaster-in-charge has specific and related experience, as well as a valid blaster’s license \nissued by a recognized licensing body for the type of blasting required. \nV. Blasting plans. Blasting plans are for quality control and record-keeping purposes, and are \nto be signed by the blaster -in-charge. Review and acceptance of blasting plans does not \nrelieve the Contractor of the responsibility for using existing drilling and blasting technology, \nand for obtaining the required results. Do not deliver explosives  to the project until the \ngeneral blasting plan is accepted. \n• General blasting plan.  Submit a general blasting plan for acceptance before drilling \noperations begin, which includes, at a minimum, the following safety and procedural \ndetails: \n• Working procedures and safety precautions for storing, transporting, handling, and \ndetonating explosives \n• Proposed product selection for both dry and wet holes and furnished manufacturer’s \nmaterial safety data sheets for all explosives, primers, initiators, and oth er blasting \ndevices \n• Typical plan and section views for both production and controlled blasting, including \nmaximum length of the shot, burden, hole spacing, hole inclination, hole depth, hole \ndiameter, subdrill depth, and powder factor \n• Proposed initiation and delay methods and times \n• Proposed format for providing all the required information for the site-specific blasting \nplans \n• Site-specific blasting plans. After the general blasting plan is accepted, the Contractor \nshall submit site -specific blasting plans f or acceptance before drilling operations \nbegin. The plan shall include the following information: \n• Site drawings showing a scaled map of the blast area and cross-sectional views that \nindicate beginning and ending stations; free -face locations; hole spacing,  diameter, \ndepth, burden, and inclination; and subdrill depth. Include any significant joints or \nbedding planes within the blast zone and incorporate this geology into the blast \ndesign. \n• Where blasting may affect nearby structures or utilities, the Contract or shall provide \nthe method of monitoring and controlling blast vibrations. \n• Provide a loading pattern diagram showing the location and amount of each type of \nexplosive to be used in the holes, including primer and initiators and the location, type, \nand depth of stemming, column heights, and overall powder factor for each type of \nloading.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3633, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "498301b9-cf96-4423-8679-46efbf76f488": {"__data__": {"id_": "498301b9-cf96-4423-8679-46efbf76f488", "embedding": null, "metadata": {"page_label": "21", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "a1ee8109-5f8f-4e2e-b736-686264825d8d", "node_type": "4", "metadata": {"page_label": "21", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "c6ca73b250fad0c40f0b7b1c6e5ed3ae298d5d0914adb4040ae79eaccc62a5bd", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-17 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \n• Provide a delay and initiation diagram showing the delay pattern, sequence, and delay \ntimes. \n• Preblast condition survey and vibration monitoring and control. When blasting n ear \nbuildings, structures, or utilities that may be subject to damage from ground or air -\nblast vibrations, the Contractor shall provide a blast vibration specialist with at least \nfive consecutive years of experience in vibration monitoring for at least three projects. \nFourteen days before blasting, the Contractor shall submit to the Engineer the name \nand qualifications of the blast vibration specialist including the following information: \n• Project names, locations, and services performed \n• Name and phone numbe r of an owner or agency contact who can verify the \nexperience of the specialist \n• Relevant qualifications and licences \nBefore blasting, the Contractor shall arrange for a pre -blast condition survey of nearby \nbuildings, structures, or utilities that may be at  risk from blasting damage, and shall use a \nsurvey method acceptable to its insurance company. Damage resulting from blasting is the \nContractor’s responsibility. All pre-blast condition survey records shall be made available to \nthe Engineer and the Contractor shall notify the Engineer and occupants of nearby buildings \nat least 24 hours before blasting. \nVibrations shall be controlled by a blast vibration specialist using properly designed delay \nsequences and allowable charge weights per delay when blasting near buildings, structures, \nor utilities that may be subject to damage from blast-induced vibrations. Trial blasts shall be \nconducted to measure vibration levels and base allowable charge weights per delay. Test \nblasts shall be conducted with blast plan mod ifications that limit ground and air -blast \nvibrations to a level that will not cause damage to nearby buildings, structures, or utilities, as \ndetermined by the blast vibration specialist. \nWhen vibration damage to buildings, structures, or utilities is poss ible, the blast vibration \nspecialist shall monitor each blast with approved seismographs and air -blast monitoring \nequipment located at acceptable locations. Seismographs capable of recording particle \nvelocity for three mutually perpendicular components of vibration shall be used. The blast \nvibration specialist shall interpret the seismograph and air -blast records to ensure that the \ndata is effectively used in the control of blasting operations. \nVI. Test Blasting. Before full -scale drilling and blasting, the Con tractor shall drill, blast, and \nexcavate one or more test sections as proposed in the blasting plan. Test blasts may be \nmade away from or at the final slope line. \nFor the cushion, or trim, method of controlled blasting, the Contractor shall space blast holes \nno more than 1.5 m apart for the initial test blast. For the pre-splitting method of controlled \nblasting, the Contractor shall space blast holes no more than 750 mm apart for the initial \ntest blast. Spacing shall be adjusted as approved. Contractor shal l use the approved \nspacing in the full-scale blasting or subsequent test blasts. \nA test blast is unacceptable when it results in fragmentation beyond the final rock face, fly \nrock, excessive vibration, air blast, over break, damage to the final rock face, or overhang. \nWhen a test blast is unacceptable, the Contractor shall revise the blasting plan and make \nan additional test blast. \nVII. Blasting. \na. General. Drill and blast according to the blasting plan. \nBefore drilling, the Contractor shall remove overburdened soil and loose rock along \nthe top of the excavation for at least 10 m beyond the hole drilling limits, or to the end \nof the cut.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3828, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1fa16851-f4c4-4702-8edb-908f25bb0de3": {"__data__": {"id_": "1fa16851-f4c4-4702-8edb-908f25bb0de3", "embedding": null, "metadata": {"page_label": "22", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "6a6bcc5a-0bd4-407a-9d74-718427c79c19", "node_type": "4", "metadata": {"page_label": "22", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "c360ac0b41e65593d10674d20f013f50d7657039ceed6de0d61886cdb455ed0f", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-18 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nAll holes shall be capped to prevent unwanted backfill. Place a stake next to each \nhole that numbers each hole and displays the total depth drilled. \nUse the types of explosives and blasting accessories necessary to obtain the required \nresults. A bottom charge may be larger than the line charges if no over break results. \nAfter blasting, the hole shall be free of obstructions for its entire depth. Charges shall \nbe placed without caving the blast hole walls. The upper portion of all blast holes shall \nbe stemmed with dry sand or other granular material passing the 9.5 -mm sieve. Do \nnot stem the hole with drill cuttings. \nFollowing a blast, the Contractor shall stop work in the entire blast area and check for \nmisfires before allowing workers to excavate the rock. \nWorkers shall remove or stabilise all cut face rock that is loose, hanging, or potentially \ndangerous. They shall sc ale by hand or machine methods, as approved by the \nEngineer. Minor irregularities or surface variations shall be left in place, if they do not \ncreate a hazard. Drill the next lift only after the cleanup and stabilisation work is \ncomplete. \nIf blasting operations trigger fracturing of the final rock face, the Contractor shall repair \nor stabilise it in an approved manner at no cost to the Owner. Repairs or stabilisation \nmay include removal, rock bolting, rock dowels, or other techniques. \nWhen any of the follow ing occur, the Contractor shall halt blasting operations to \nperform additional test blasts: \n▪ Slopes are unstable\n▪ Slopes exceed tolerances or overhangs are created\n▪ Back slope damage occurs\n▪ Safety of the public is jeopardized\n▪ Property or natural features are endangered\n▪ Fly rock is generated\n▪ Excessive ground or air -blast vibrations occur in an area where damage to\nbuildings, structures, or utilities is possible \nb. Drill logs. Drill logs submitted by the Contractor shall include the following:\n▪ A blast plan map showing designated hole numbers\n▪ Individual hole logs completed and signed by the driller that show total depth\ndrilled; depths and descriptions of significant conditions encountered during \ndrilling that may affect loading, such as water or voids; and the date drilled \nc. Controlled blasting. When test blasts indicate the need for controlled blasting, the\nContractor shall use controlled blasting methods to form the final rock cut faces when the \nrock height is more than 3 m above ditch grade and the staked slope has a ratio of 2:1 or \nsteeper. \nControlled blasting includes only those holes drilled on the row furthest from the free-face \nand that are drilled on the design slope. \nDown-hole angles or fan drill blast holes shall be used for pioneering the tops of rock cuts \nor preparing a working platform for controlled blasting. Use the diameter of the blast hole \nand spacing established for controlled blasting during the test blasts. \nDrill controlled blast holes no larger than 100 mm in diameter along the final rock fac e \nline; within 75 mm of the proposed surface location; and at least 10 m beyond the \nproduction holes to be detonated or to the end of the cut.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3240, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "da6d4d86-5e1d-404f-acfb-1c8d929c1a69": {"__data__": {"id_": "da6d4d86-5e1d-404f-acfb-1c8d929c1a69", "embedding": null, "metadata": {"page_label": "23", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "dc47a7a1-14b8-42e1-b66f-2b2837ecd5b4", "node_type": "4", "metadata": {"page_label": "23", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "b9d2629782902d7c36309edc9a8036817cf265f6dd95ea7b67568895568f3f64", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-19 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nDrilling equipment with mechanical or electrical -mechanical devices that accurately \ncontrol the angle the drill e nters the rock shall be used. Select a lift height and conduct \ndrilling operations so the blast hole spacing and down-hole alignment does not vary more \nthan 225 mm from the proposed spacing and alignment. When more than 5 percent of \nthe holes exceed the va riance, reduce the lift height and modify the drilling operations \nuntil the blast holes are within the allowable variance. The maximum lift height is 15 m. \nA 600 mm offset is allowed for a working bench at the bottom of each lift for drilling the \nnext lower controlled blasting hole pattern. Adjust the drill inclination angle or the initial \ndrill collar location so the required ditch cross-section is obtained when the bench is used. \nDrilling 500 mm below the ditch bottom is allowed for removing the toe. \nDo not use bulk ammonium nitrate and fuel oil for controlled blasting. \nPre-splitting delays the nearest production blast row at least 25 milliseconds after blasting \nthe presplit line. Pre-split a minimum of 10 m ahead of the production blasting zone. \nCushion, or trim, blasting can delay the cushion blast row from 25 milliseconds to 75 \nmilliseconds after blasting the nearest production row. \nd. Production blasting. Drill the row of production blast holes closest to the controlled blast\nline parallel and no closer th an 2 m to the controlled blast line. Do not drill production \nblast holes lower than the bottom of the controlled blast holes. \nDetonate production holes on a delay sequence toward a free-face. \nVIII. After blast reports.\nWithin three days after a blast and before  the next blast, the Contractor shall submit an \nafter-blast report that includes the following: \na. Results of the blast and whether or not blasting objectives were met. If blasting objectives\nwere not met, the Contractor shall submit proposed changes to futur e site -specific \nblasting plans that shall produce acceptable results and proposed repair or stabilisation \nplans for unstable or blast-damaged back slopes. \nb. Blasting logs that include the following:\ni. All actual dimensions of the shot, including blast hole depths, hole diameter, burden,\nspacing, subdrilling, stemming, powder loads, and timing \nii. A drawing or sketch showing the direction of the face, or faces, and the physical\nshot layout \niii. If a seismograph was used, the Contractor shall provide the following:\n(1) Identification of instrument used\n(2) Name of qualified observer and interpreter\n(3) Distance and direction of recording station from blast area\n(4) Type of ground recording station and material on which the instrument is sitting\n(5) Maximum particle velocity in each direction\n(6) A dated and signed copy of the seismograph readings\n(7) Post-blast condition survey\n(8) Results of air-blast monitoring\n(9) Results of the post-blast condition survey\n2.4.2.5 Subexcavation \nUnsuitable material, including soft, m uck, or vegetative material, shall be excavated by the \nContractor to the limits designated by the Engineer. Cross -sections shall be taken in accordance", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3249, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "caf7b161-f106-43cb-bd7f-ffb3e41f5392": {"__data__": {"id_": "caf7b161-f106-43cb-bd7f-ffb3e41f5392", "embedding": null, "metadata": {"page_label": "24", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "4f871724-bd44-499a-af26-6fb0fe053ef3", "node_type": "4", "metadata": {"page_label": "24", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "fc3d36fa1060092e8cdbcd033415be1f7bee9f55b98eaf0fa369bae4786dd9d6", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-20 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nwith Section 2.1. Prevent unsuitable material from becoming mixed with the backfill. Unsuitable \nmaterial shall be disposed of in accordance with Section  ******* Sub-excavated limits shall be \nbackfilled with suitable load bearing material in accordance with Section 2.5.3. \nWhen water fills an area after the removal of unsuitable materials, the Contractor shall drain the site \nso that any backfill may be compacted. If drainage is not possible, the Contractor sha ll use free \ndrainage material which can be hydraulically compacted for backfilling in water.  Backfill method and \nmaterials shall be approved by the Engineer, and shall be carried out on the Contractor’s expense.  \n2.4.2.6 Slope Treatment \nEarth slopes shall be finished to reasonably smooth surfaces and shall be free of all debris and loose \nmaterial. All shattered or loosened material shall be removed from rock cut slopes. \nTops of all roadway cut slopes, except solid rock cuts, shall be rounded in accordance with the  \nContract plans. \nSurfaces of earth slopes shall be left in a uniform and smooth condition, trimmed, and graded to no \nmore than ±50 mm of the neat lines shown on the Contract plans . Where the slopes of earth \nexcavations are to be covered with topsoil or oth er agricultural planting soil, the excavated surface \nshall be caterpillar tread -walked in a vertical direction, or similar measure, to roughen the surface \nand help hold the upper topsoil layers. \nIf a layer of earth covers a rock cut, the slope shall be rounded above the rock as if it were an earth \nslope. All work required to complete slope treatment, including excavation, haul, and slope rounding, \nshall be included in the unit price for roadway excavation. \n******* Disposal of Unsuitable or Excess Material \nUnsuitable or excess material shall be disposed of off -site at disposal sites furnished by the \nContractor and approved by the Engineer. Waste material shall be shaped and compacted in its final \nlocation. \nIf the Cont ractor or Engineer finds any evidence of petroleum or other hazardous products \ncontamination of any excavated material by visual, olfactory, or other means, the potentially \ncontaminated material shall be stockpiled separately and sampled and analysed to de termine \ndisposal options. \nMaterial that is excavated and deemed unsuitable or as excess material shall be placed in a covered \ntransport and hauled to the disposal site. Excavated material shall not spill, overflow, or release \nduring transport to the disposal site. \nSoil that is designated for landfill disposal and contains free water shall be stabilised using lime, \ncement, or fly ash, where required by the disposal facility. \nSoil shall not be transported off-site in containers with water draining from the container. \n2.4.2.8 Wasting Material \nIf the Contractor wastes excavated material needed for the embankment or other fill, it shall be \nreplaced at no expense to the Owner with material the Engineer approves. \n2.4.2.9 Roadway Ditch and Pond Excavation \na. Description\nThis work consists of excavating permanent open ditches and ponds to the required lines, grades, \nand cross -sections as shown on the Contract plans. It also includes disposing of all excavated \nmaterial regardless of its nature or type; unless excavated material is approved as borrow material.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3447, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ad28c9c3-9998-47c6-84a0-d7935d58cace": {"__data__": {"id_": "ad28c9c3-9998-47c6-84a0-d7935d58cace", "embedding": null, "metadata": {"page_label": "25", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "fcce2a2f-7f38-4f2a-943a-67506fa80ae0", "node_type": "4", "metadata": {"page_label": "25", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "f836c3504db58324cbb79c80f0bd75e7b61337928585f680fca274f1562c0a79", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-21 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nb. Construction requirements \nBefore excavating any open ditch or pond, the Contractor shall clear and grub the area in accordance \nwith Section 2.2. \nWhere the excavated ditch or pond material is approved for use as embankment material in \naccordance with the requirements of Section  *******, the material shall be used to build dikes or \nberms as shown on the plans. Dikes and berms shall be constructed in accordance with the \nrequirements for roadway embankment compaction in Section 2.5.3. \nExcess excavated material may be disposed of by dressing or spreading above the back slopes of \nditches and outside of dikes and berms, as approved by the Engineer. Otherwise, the Contractor \nshall dispose of excess excavation material in accordance with Section *******. \nAt each transition from cut to fill, the  Contractor shall divert any roadway ditch away from the \nembankment in natural ground. Ditches shall never permit water to flow into or upon embankment \nmaterial. \n2.4.2.10 Borrow Excavation \nAll suitable roadway excavation shall be used by t he Contractor in embankment construction, and \nborrow excavation shall not be used when it results in excess roadway fill material. \nBorrow sources shall be preapproved by the concerned authorities and the Engineer. Approval shall \nbe based on the Contractor’ s submittal of location, sample testing, estimation of quantities, written \napproval of the borrow source owner and/or regulating authority, and restoration of borrow source \nupon completion as shown on the shop drawings. For borrow sources to be accepted, t he material \nshall meet the requirements for the applicable load -bearing or non -load-bearing material \nrequirements in Section 2.5.2. \nDevelop and re store borrow sources in accordance with the Engineer -approved methods. Do not \nexcavate beyond the established limits. When applicable, the Contractor shall shape the borrow \nsource to permit accurate measurements when excavation is complete. Borrow sources shall be left \nin a manner that is pleasing to the eye, with excavated slopes and surfaces left in a smooth and \nuniform shape at geotechnical stable angles.  No extra payment will be made for restoration and \nfinal grading of the borrow source. \n2.4.2.11 Structural Excavation \na. General \nContractor shall include all operations necessary to excavate, as required for the construction of the \nstructures, and as indicated on the Contract plans. \nThis work shall consist of the necessary excavation for construction of minor structures including \nstorm drain and utility  structures and wall items as otherwise specified in these general \nspecifications. Contractor will not be paid for excavation of any minor structures or portions of minor \nstructures that are constructed within the volume of the roadway embankment excavation or fill \nprism. This work shall include necessary dewatering, sheeting, bracing, construction of cribs and \ncofferdams, furnishing materials, and the subsequent removal of cribs and cofferdams. \nExcavation to remove and replace unsuitable material encountered below the structure foundation \nelevation shall meet the requirements of Section 2.4.2.5. Depending on conditions, the Engineer \nmay require the use of alternative materials as replacement fill for sufficient foundation strength. This \nmay include one of the following materials: \n1. Load-bearing embankment material meeting the requirements of Section ******* \n2. Structural backfill material meeting the requirements of Section ******* \n3. Pervious backfill material meeting the requirements of Section ******* \n4. Controlled density fill meeting the requirements of Section 4.3.10.3 of Chapter 4, Concrete \nWorks, of these Standards Specification.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3847, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8422aec1-ba6a-4e9a-839d-64c5af06b066": {"__data__": {"id_": "8422aec1-ba6a-4e9a-839d-64c5af06b066", "embedding": null, "metadata": {"page_label": "26", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "df95b1c3-14a4-451f-8c6c-883a9ed62b67", "node_type": "4", "metadata": {"page_label": "26", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "516fbb1a15fccbe16796c548353ca53edfd6add212ee4656c9a6bbfe9201c881", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-22 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nb. Excavation Support\nAll excavations measuring 1.5 m or more in depth, trench or otherwise, shall be laid back to a stable \nslope or supported. Excavation support systems are subject to the approval of the Engineer. Prior to \ncommencing any structure excavation work 1.5 m or more in depth, the Contractor shall design and \nsubmit detailed shop drawings of the structure excavation support systems and proposed methods \nto the Engineer. These submissions shall show support member materials, sizes, spacing, and \nEngineering calculations to validate the design of the above, including the maximum theoretical \ndeflections of the support members. \nSupport systems shall be designed in such a manner that no sheeting, struts, or any other support \nmembers extend through surfaces exposed in the finished construction, and no shoring or bracing \nis placed under permanent structures. \nEngineering calculations shall be in English and shall show lateral earth pressures for the full \nexcavation depths; forces at various stages of support during installation, removal, and concrete \nplacement; anticipated equipment loads; surcharge loads  of any description; and the maximum \ndesign loads carried by various members of the support system and strut pre-load forces. \nIn the event the proposed structure excavation support system includes tieback anchors, the \nContractor's submittal drawings shall show the profile of the soil in which each anchor is to be \ninstalled, the design load for the full depth of excavation, the maximum design and proof loads, \nsurcharge loads of any description, equipment loads, forces at various stages of support during \ninstallation and removal, and the criteria proposed for deformations under proof loads. \nWhere a proposed system of tieback anchors projects beyond the vertical projection of the Contract \nlimit lines indicated on the Contract plans onto the adjoining property, the Contractor shall obtain the \npermission of the owner in writing, and submit such permission to the Engineer at the time the shop \ndrawings of the support system are submitted. \nc. Excavation Limits\nSo that cross-sectional elevations and measurements may be taken of the undisturbed ground, the \nContractor shall notify the Engineer sufficiently in advance of any structure excavation. Natural \nground adjacent to the structure shall not be disturbed without the Engineer’s permission. \nStructures shall be excavated to the lines and grades or elevations indicated on the Contract plans, \nor as described in the bill of quantities. Excavations shall be of sufficient size to permit the placing of \nstructures or structure footings of the full width and length indicated. \nUnless otherwise specified, the excavation limits for structural excavation sha ll be from the bottom \nlevel (formation level) of the roadway excavation which falls within the roadway excavation width. \nBoulders, rocks, and any other objectionable material encountered during excavation shall be \nremoved. \nAfter each excavation is completed, the Contractor shall notify the Engineer. No footing, bedding \nmaterial, or structure shall be placed until the Engineer has approved the depth of exc avation and \nthe character of the material on which the foundations shall bear. \nd. Disposal of Excavated Materials\nAll excavated material at structures shall be loaded and hauled away to an approved disposal area \nin accordance with Section *******, unless the excavated materials are approved by the Engineer \nfor use as borrow material meeting the applicable requirements of Section 2.5.2. \n2.4.2.12 Manual Excavation \na. General\nAll manual or hand excavation and other required work to locate existing utilities or services within \nthe limits of the Contract or at off-site locations shall be performed by the Contractor in accordance \nwith the requirements of these general specifications and as directed by the Engineer.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 4019, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "af0e4d59-1c51-41d6-8195-b361de7781ec": {"__data__": {"id_": "af0e4d59-1c51-41d6-8195-b361de7781ec", "embedding": null, "metadata": {"page_label": "27", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "e56fbb7d-f5f4-4147-b65e-9cf7630dafd4", "node_type": "4", "metadata": {"page_label": "27", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "7d40a660d893d769dbe13e46320c1d464580699cc2cfce72b70c52e875061ba4", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-23 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nExisting utilities and services to be located by manual excavation shall include, but not be limited to, \nall water mains and electric lines, both power and lighting; telephone; and others that may be \nencountered under the Contract or at off-site locations. \nb. Coordination with Utility Departments \nPurpose of manual excavation is to determine the existence and location, including depth, size, \nshape, composition, and condition, of all existing utilities and services. Immediately after uncovering \nexisting utilities, the Contractor shall coordinate with the respective utility departments and agencies \nto obtain their assistance, if necessary, for the accurate identification of each uncovered utility line. \nInformation obtained during this process shall be recorded and shown on working or shop drawings \nthat the Contractor shall submit to the Engineer and the relevant utility agencies. Utility agencies \nshall require this information to finalise relocation or protection plans. \nc. Procedure \nBefore commencing manual excavation, the Contractor shall obtain the Enginee r’s approval and \nobtain specific work permits from respective utility agencies for manual excavation in close proximity \nto important utility lines such as, but not limited to, 11 kV, 33 kV circuits, 132 kV circuits, water \ntransmission and distribution main lines, and telephone and gas lines. \nContractor shall furnish the Engineer with copies of all work permits, field notes, and shop drawings \nwith the details of all utilities and services located by manual excavation, as recorded by the \nContractor. \nWidth of manually excavated trenches shall be such that a workman can excavate safely and \nefficiently to a depth determined as reasonable by the Engineer in consideration of existing utilities \ninformation. \nManual excavations shall be carried out in the presence of the Engineer, taking all precautions to \nprevent damages to services, properties, and persons. Any damage resulting from the negligence \nof the Contractor shall be repaired at the Contractor’s expense. This is in addition to any indemnities \nstipulated in these specifications dealing with public utilities and safety. \nContractor shall provide all tools, labour, equipment, and accessories required to complete the \nmanual excavation. Further, the Contractor shall provide all the materials, labour, and equipment \nnecessary to protect the existing utilities, as well as any shoring, sheeting, dewatering, and any other \nmeans required for protection during manual excavation. \nAll materials removed shall be disposed of by the Contractor in accordance with Section  *******, \nunless the excavated materials are approved by the Engineer as material meeting the applicable \nrequirements of Section 2.5.2. \nAll manually excavated trenches within traffic and pedestrian pavement areas, other structure \nfoundation areas, and where necessary for adjacent structure or utility lateral and vertical bearing \nsupport shall be backfilled with load -bearing embankment mate rial in accordance with the \nrequirements of Article c of Section *******. Trenches within non -load-bearing areas shall be \nbackfilled in accordance with Section *******. \nContractor shall place and compact material with care so as not to damage existing utilities. \n2.4.2.13 Unclassified Excavation \nUnclassified excavation shall include any and all  materials encountered during the construction of \nthe Work. This classification shall not apply to any material, which has been classified and bid upon \nunder any of the foregoing classifications, \"Borrow Excavation\", \"Rock Cut\", \"Structure Excavation\" \nor “Manual Excavation”.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3778, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "bbe148aa-4808-4139-a452-a9a9b99aacec": {"__data__": {"id_": "bbe148aa-4808-4139-a452-a9a9b99aacec", "embedding": null, "metadata": {"page_label": "28", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "aaa61bd5-f7d2-43c2-84f5-a093410638aa", "node_type": "4", "metadata": {"page_label": "28", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "f0f2af5bfdb27450672ffbcda9eb39bb0362d3a7c3769b7c0b9aef5f0cd51ed3", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-24 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n2.5 Embankments and Backfill \n2.5.1 Description \nEmbankment and backfill requirements shall apply to the construction of roadway embankments, \nincluding the widening of embankment sections and the preparation o f the areas upon which \nembankment material is to be placed; the construction of dikes and berms; the placing and \ncompacting of material where unsuitable material has been removed; backfill for structures and the \nplacing of non-load-bearing, topsoil and sweet sand materials in landscaping areas; and other areas \nnot under roadway and pedestrian pavements. \nBefore the start of construction, the Contractor shall submit plans showing the proposed method of \nwork, the selection of materials, the method of compaction, and any other matter that may affect the \nconstruction of the embankment or sequence of operations. \n2.5.2 Materials \n2.5.2.1 Unsuitable Material \nUnsuitable material includes but not limited to \n• Topsoil,\n• Organic soils,\n• Vegetative material,\n• Trash,\n• Contaminated soil,\n• Soluble material such as gypsum and salt, very fine sand, non-cohesive silt, organic clay\nand highly dispersive soils. \nDispersivity potential of soil shall be determined by test ASTM D4647. Any material classified other \nthan ND1 or ND2 (non dispersive) will be considered as unsuitable.  \nMaterials with soluble substances more than 3% (by test method ASTM D4542) and organic content \nmore than 5% (by test method ASTM D2974) by weight of dry material are all considered as \nunsuitable and shall not be used for construction of embankment.  \nCollapsible soils are also considered as unsuitable material and shall be treated before construction \nof embankment. Collapse index is determined according to ASTM D5333.  \n******* Embankment Material (Load-Bearing) \nLoad-bearing embankment material shall consist of suitable material taken from the roadway \nexcavation or the borrow source. Load -bearing embankment material shall consist of granular \nmaterial free of excess moisture and other unsuitable material conforming to the following: \n1. Maximum particle size: not to exceed one-third of placement layer thickness\n2. Materials shall be classified according to AASHTO M 145. In general, suitable materials shall\nmeet soil classification: A-1, A-3, or A-2-4. \n3. Material that can be compacted to 95 percent of the maximum density as required in Article\nb of Section ******* \n4. Material shall have a maximum plasticity index of  6 and a soaked CBR after 4 days soaking\nis not less than 20% when compacted to 95% of MDD. \nSuitability tests including material gradation, plasticity and CBR tests shall be done in accordance \nwith Table 2-12.  All suitability testing will be done with one test each per 1000 m 3 of embankment \nmaterial.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 2862, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c6da6039-6f7d-4af8-8515-2e4fb76f2dd1": {"__data__": {"id_": "c6da6039-6f7d-4af8-8515-2e4fb76f2dd1", "embedding": null, "metadata": {"page_label": "29", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "43e75014-6d93-4c1a-a869-e26d6df8aac2", "node_type": "4", "metadata": {"page_label": "29", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "c2df86d22396ad73d344309556083042d377477ab492c65ed32709a4ee3a643d", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-25 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nEmbankment material placed adjacent to bridge abutments, retaining and wing walls, and other \nminor structures, as shown on the Contract plans or as directed by the Engineer, shall be classified \nas structure backfill and shall conform to the requirements in Section  *******, except for drainage \nlayer backfills when shown on the Contract plans, which shall conform to the requirements of Section \n******* for pervious backfill. \n******* Rock Fill Material \nRock fill material shall be made of strong, hard, durable, and clean pieces of sound rock, having the \nfollowing requirements:  \n1. Point load strength index (Is50) (per ASTM D5731) greater than 1 mPa\n2. Maximum wet/dry strength variation of 35 %\n3. Maximum and minimum dimensions are determined based on the application, however the\nmaximum dimension for embankment construction shall be 600 mm. \nRock fill material borrow source(s) shall be pre-identified and submitted with samples and test results \nmeeting the above re quirements for review and approval by the Engineer prior to delivery of the \nmaterial to the site.   \nTypically, rock fill material excavated from within the roadway excavation prism will be incorporated \nwithin the embankment fill, if meeting the requirement s as specified herein.  Otherwise it will be \ndisposed of by the Contractor at the sites and locations as approved by the Engineer. \n******* Structural Backfill \nStructural backfill shall be a uniform mixture of gravel and/or stone fragments with sand, silt and clay, \nconforming to the following requirements: \n1. A maximum particle size of 53 mm\n2. Material gradation meeting the requirements in Table 2-3\n3. Liquid limit: 25  maximum\n4. Uniformity coefficient: >10\n5. Plasticity index:  4 maximum\n6. Sand equivalent (AASHTO T 176): 25 Minimum\n7. Loss by abrasion (AASHTO T 96):  40 Maximum\n8. Material that can be compacted to 95 percent of the maximum density as required in Article\nb of Section ******* with a maximum dry density:  minimum 20 kN/m3 \n9. Minimum soaked CBR (remoulded sample after 4 -days soaking, AASHTO T193 : 60%\nminimum at 95% compaction \nMaterial gradation, liquid limit and plasticity tests shall be done in accordance with Table 2-12.  All \nsuitability testing will be done with three t ests each per 1000 m3 of select material except for  \ngradation testing which shall be one test per 1000 m3. \nTable 2-3: Structural backfill gradation \nSieve size Percent by mass passing designated sieve \n(AASHTO T 27 and T 11) \n53 mm 100 \n37.5 mm 75-100", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 2624, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "910013af-97b4-4dca-a7c6-28a5a315b595": {"__data__": {"id_": "910013af-97b4-4dca-a7c6-28a5a315b595", "embedding": null, "metadata": {"page_label": "30", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "2ff2f7bd-3727-4bc7-8c10-6b4f8e764bd0", "node_type": "4", "metadata": {"page_label": "30", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "f96731af5f8da88073156fedc5dc5cd11c4012258922fd8b30216fa10c986c29", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-26 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nSieve size Percent by mass passing designated sieve \n(AASHTO T 27 and T 11) \n25.0 mm 55 -85 \n19.0 mm 50 - 80 \n9.5 mm 40 - 70 \n4.75 mm 30 - 60 \n2.0 mm 20 - 50 \n425 µm 10 - 30 \n75 µm 5 - 15 \n******* Pervious Backfill \nPervious backfill material shall be clean, hard, durable sand, gravel or crushed stone free from \norganic material, clay balls or other deleterious substances conforming to the gradation requirements \nin Table 2-4 Pervious backfill material used as a drainage layer against soil retaining structures (such \nas bridge abutments and cut or fill retaining walls) shall meet the gradation requirements of Table \n2-5 \nTable 2-4: Pervious backfill gradation  \nSieve size Percent by mass passing designated \nsieve (AASHTO T 27 and T 11) \n53 mm 100 \n37.5 mm 95 - 100 \n19 mm 50 to 100 \n9.5 mm 15 - 55 \n2.36 mm 0 - 5 \n75 µm 0 - 3 \nTable 2-5 Pervious backfill material for use as drainage layer against soil retaining \nstructures such as bridge abutments and retaining walls \nSieve size Percent by mass passing designated sieve \n(AASHTO T 27 and T 11) \n75 mm 100 \n53 mm 90 - 100 \n37.5 mm 35 - 70 \n25.0 mm 0 - 15", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1236, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "3e7de4a9-2b51-47b1-9802-b15ca7eb102f": {"__data__": {"id_": "3e7de4a9-2b51-47b1-9802-b15ca7eb102f", "embedding": null, "metadata": {"page_label": "31", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "bf50f4ad-d371-415b-bd50-13f665f99ec3", "node_type": "4", "metadata": {"page_label": "31", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "baf283bfaff2a9dabc04aecfeb4fca14cff020f5b149f94083554c2430deee97", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-27 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nSieve size Percent by mass passing designated sieve \n(AASHTO T 27 and T 11) \n12.5 mm 0 - 5 \n******* Material for Free-Draining Blankets \nMaterial for free draining blankets, used for constructing vertical drains or as a base layer for \nembankments constructed over soft foundations, shall consist of either a clean processed aggregate \nmaterial meeting the grad ation requirements of Table 2 -6 or natural dune sand obtained from a \nclean, uniformly open graded and non-contaminated source. \nTable 2-6:  Gradation for free-draining blanket material \nSieve size Percent by mass passing designated \nsieve (AASHTO T 27 and T 11) \n75 mm 100 \n19 mm 95 - 100 \n4.75 mm 50 - 100 \n1.18 mm 20 - 85 \n0.3 mm 3 -30 \n0.15 mm 0 - 7 \n0.075 mm 0 - 3 \n2.5.2.7 Non-Load-Bearing Material \nNon-load-bearing material shall include topsoil and sweet sand material in accordance with the \nrequirements of Article a of Section 2.5.2.7 \nNon-load-bearing material shall also consist of roadway excavat ion or other borrow material \napproved by the Engineer and deemed suitable for use as fill material for raising existing areas not \nlocated under roadway pavements, roadway embankments, pedestrian pavements, or other \nstructures, and to grades shown on the Contract plans. This material may also fill low -lying natural \nareas to grades suitable for placing upper topsoil or sweet sand courses within landscaped areas, \nin accordance with the Contract plans or as directed by the Engineer. \nMaterial shall be reasonably free from salts, refuse, roots, heavy or stiff clay, stones or rocks larger \nthan 30 mm in size, sticks, brush, litter, or other deleterious materials or substances. \na. Topsoil and Sweet Sand Material \nTopsoil shall consist of the material excavated and stockpiled in accordance with Section *******. \nSweet sand shall consist of borrow material supplied from sources approved by the Engineer, which \nhas been analysed by the Contractor as being suitable for agricultural purposes. Sweet sand shall \nbe obtained from well -drained inland dunes and shall be reasonably free from salts, refuse, roots, \nheavy or stiff clay, stones or rocks larger than 30  mm in size, noxious seeds, sticks, brush, litter, \ndebris, or other deleterious materials or substances. \nTopsoil and sweet sand borrow material destined for placement on the surface of vegetated \nlandscape areas and existing soil surfaces to remain in place within project constructed vegetated \nlandscape areas shall be tested and amended, as necessary, to comply with the requirements of", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 2660, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "8abf17de-2e47-4ab0-a121-f0b9b4e2abbb": {"__data__": {"id_": "8abf17de-2e47-4ab0-a121-f0b9b4e2abbb", "embedding": null, "metadata": {"page_label": "32", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "e350bcb1-e8e7-4ba2-b32b-63ac62c39a39", "node_type": "4", "metadata": {"page_label": "32", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "110f8ae37829012ec453c57a33336e656d1b5ea0df8655be62fdd574d23845a2", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-28 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nplanting soil described in Section 1 ******* of Chapter 13, Landscaping and Irrigation of these \nStandard Specifications. \n2.5.2.8 Prefabricated Vertical Drains \nPrefabricated vertical drains (PVD) for embankment foundation water pressure relief shall consist of \na continuous plastic drainage core wrapped in a non-woven geotextile material, as approved by the \nEngineer.   \nPVDs shall be of newly manufactured materials from an approved manufacturer, and shall comply \nwith the requirements as indicated in Table 2-7.  For each type of drain to be used, the manufactured \nPVD to be used on site shall be subjected to all the tests in the table.  The types of PVD to be used \nat site shall be approved by the Engineer. \nGeotextile shall be capable of resisting all bending, puncturing and tensioning subjected during \ninstallation and design life of the drain.  Core shall be made of continuous plastic material fabricated \nto facilitate drainage along the axis of the vertical drain.  Core shall be a profiled strip with or without \nperforation or a profiled mat with an open or closed structure.  \nContractor shall indicate the proposed source of the materials prior to delivery to site.  Contractor \nshall also submit samples and manufacturer’s certificates to verify the physical, mechanical and \nhydraulic properties of the drain to be used for the Engineer’s approval. \nPrior to installation and at the discretion of the Engineer, an individual test sample shall be cut from \nat least one roll selected at random to represent each batch or every 100,000 meters, whichever is \nless.  Individual sample shall be not less than 3 m in length and shall be full width.  Samples shall \nbe tested for the properties listed in the following table .  Should any individual sample randomly \nselected fail to meet the specification, then that roll shall be rejected and two additional samples \ntaken at random from two other rolls.  If either of these two additional samples fail, then the entire \nbatch of vertical drains represented by the samples will be rejected by the Engineer. \nTable 2-7:  Vertical filter drain properties \nProperties Required value Test \ndesignation \nDrain properties \nWidth (mm) 100 ± 2 \nThickness (mm) 3 to 4 \nDischarge capacity for straight drain at hydraulic gradient \ni = 0.1 and 250 kPa* (m3/s) ≥ 50x10-6 ASTM D4716 \nDischarge capacity for buckled drain at hydraulic gradient \nI = 0.1 and 150 kPa* (m3/s) ≥ 15 x10-6 ASTM D4716 \nTensile strength (kN) 1.5 @ 10 % \nstrain ASTM D4595 \nElongation at break (%) ≥ 2 \nElongation at 0.5 kN (%) ≤ 10 ASTM D4362 \nGeotextile (Class 1 as per AASHTO M 288) \nGrab tensile strength (N) > 950 ASTM D4632", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 2774, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e9d58db3-f5e9-427d-bc47-ebca4ddee3c4": {"__data__": {"id_": "e9d58db3-f5e9-427d-bc47-ebca4ddee3c4", "embedding": null, "metadata": {"page_label": "33", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "af107a73-6b56-458a-9cc6-9c6ae0e4271e", "node_type": "4", "metadata": {"page_label": "33", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "e66e9f1c40e4edce2d49473b128b791022a25c43d21cf0d878fd56455df2b506", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-29 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nProperties Required value Test \ndesignation \nElongation at break  >70 % ASTM D4632 \nTrapezoidal tear strength (N) 375 ASTM D4533 \nPuncture resistance (N) > 2000 ASTM D6241 \nBurst strength (kN/m2) > 900 ASTM D3786 \nPermeability at 100 mm head (m/s) ≥ 1x10-4 ASTM D4491 \nPermitivity (s-1) > 0.4 ASTM D4491 \nApparent pore size, O90 (micron)** > 80 ASTM D4751 \nFabric Weight (g/m2) 180 Manufacturer \n*If (drain depth + 2 x fill height ) > 25 m but < 50 m, pressure shall be 500 kPa for straight drain \nand 350 kPa for buckled drain. \n**Alternatively, O95 shall be < 90 micron \nDrains shall be free of defects, rips, holes, or flaws. During shipment and storage, drains shall be \nwrapped in a heav y-duty protective covering, and the storage area shall protect the drain material \nfrom sunlight, mud, dirt, dust, debris, and detrimental substances. \n******* Construction Geotextile \nGeotextile fabrics shall consist only of long-chain polymeric fibers or yarns formed into a non-woven, \nneedle-punched stable network such that the fibers or yarns retain their position relative to each \nother during handling, placement, and design service life. At least 95% by weight of the material \nshall be polypropylene, polyolefin or polyesters. These materials shall be free from defects or tears. \nMaterials shall also be free of any treatment or coating that might adversely alter its hydraulic or \nphysical properties after installation. \nThread used for sewing geotextile shall consist of high -strength polypropylene, polyester, or \npolyamide. Nylon threads sh all not be allowed. Thread used to sew permanent erosion -control \ngeotextile, and to sew geotextile seams in exposed faces of temporary or permanent geotextile \nretaining walls, shall also be resistant to ultraviolet (UV) radiation. Thread shall be of contra sting \ncolour to that of the geotextile itself. \nGeotextile shall meet the application requirements of AASHTO M 288 and the specific requirements \nincluded in Table 2-8 through Table 2-12, in accordance with its intended use. \nAll geotextiles used in construction which are to receive an overlying laye r of rock fill such as, but \nnot limited to, pervious backfill, rip rap, boulders or other similar covering material shall be protected \nfrom damage due to the rock fill by placement of 50 mm thick layer of natural screened sand with a \nnominal size between 3 mm and 5 mm, as approved by the Engineer.    \nContractor shall submit the following information regarding each geotextile material’s proposed use \nto the Engineer: \n1. Manufacturer’s name and current address \n2. Full product name \n3. Geotextile structure, including fiber or yarn type \n4. Geotextile polymer types", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 2808, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "1dfaa977-14d9-413d-85d9-b7b81dbcdf28": {"__data__": {"id_": "1dfaa977-14d9-413d-85d9-b7b81dbcdf28", "embedding": null, "metadata": {"page_label": "34", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "492ee8a7-4a31-4f2b-99fb-f87a7c95f908", "node_type": "4", "metadata": {"page_label": "34", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "a546b1acd6914cbe5a1613c389680002f1c554105fba1a8d6a6c812ec85ce76a", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-30 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n5. Proposed Geotextile uses\n6. Certified test results for minimum average roll values\nTable 2-8: Geotextile for underground drainage \nGeotextile property \nASTM \ntest method \nGeotextile property \nrequirements \nNon-woven \nFabric weight D5261 180 g/m2 minimum \nGrab tensile strength, in \nmachine and x-machine \ndirection \nD4632 700 N minimum \nGrab failure strain, in \nmachine and x-machine \ndirection \nD4632 ≥ 50 percent \nPuncture resistance D6241 1375 N minimum \nTear strength, in machine \nand x-machine direction D4533 250 N minimum \nUV radiation stability D4355 \n50 percent strength retained \nminimum, after 500 hours in a \nxenon arc device \nTable 2-9: Geotextile for underground drainage-opening size properties \nGeotextile \nproperty \nASTM test \nmethod \nGeotextile property requirements \nPercent in situ Soil Passing 0.075 mm \n <15  15 to 50 >50\nAOS D4751  < 425 µm  < 212 µm \nstandard sieve  < 150 µm \nWater \npermittivity D4491 0.5 sec-1 \nminimum \n 0.2 sec-1 \nminimum  0.1 sec-1 minimum \nTable 2-10: Geotextile for separation or soil stabilization \nGeotextile property ASTM test method \nGeotextile property requirements \nSeparation Soil stabilization \nWeight D5261 180 g/m2 minimum  200 g/m2 minimum \nThickness D5199 1.7 mm minimum 1.7 mm minimum \nAOS D4751 Standard sieve  212 \nµm max. \nStandard sieve 212 \nµm max. \nWater permittivity D4491 0.02 sec-1 minimum  0.05 sec-1 minimum", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1494, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "5e742965-a09b-46e3-afb4-3b70adcf4d20": {"__data__": {"id_": "5e742965-a09b-46e3-afb4-3b70adcf4d20", "embedding": null, "metadata": {"page_label": "35", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "2476d2f1-5efb-417d-a974-c73b2c9b9198", "node_type": "4", "metadata": {"page_label": "35", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "17b27dbc6926e1847cb07f6d0a11caf8fac728652022d5df968dcd8d94de4fda", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-31 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nGeotextile property ASTM test method \nGeotextile property requirements \nSeparation Soil stabilization \nGrab tensile strength, \nin machine and x -\nmachine direction \nD4632 1,400 N minimum 1,500 N minimum \nGrab failure strain, in \nmachine and x -\nmachine direction \nD4632 < 50 percent < 50 percent \nPuncture resistance D6241 2,750 N minimum 2,800 N minimum \nTear strength, in \nmachine and x -\nmachine direction \nD4533 500 N minimum  600 N minimum \nUV radiation stability D4355 50 percent strength retained minimum, after \n500 hours in xenon arc device \nTable 2-11: Geotextile properties for retaining walls and reinforced slopes \nGeotextile property ASTM test method Geotextile property \nrequirements \nWeight D5261  180 g/m2 minimum \nThickness D5199  1.7 mm minimum \nAOS D4751 Standard sieve  212 µm max. \nWater permittivity D4491 0.02 sec-1 minimum \nGrab tensile strength, in \nmachine and x -machine \ndirection \nD4632 1,200 N minimum \nGrab failure strain, in machine \nand x-machine direction D4632 < 50 percent \nSeam breaking strength D4632 1,000 N minimum \nPuncture resistance D6241 2,200 N minimum \nTear strength, in machine and \nx-machine direction D4533 400 N minimum \nUV radiation stability D4355 \n70 percent for polypropylene \nand polyethylene, and 50 \npercent strength retained \nminimum for polyester, after \n500 hours in a xenon arc \ndevice", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1467, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "c1c1580b-839c-4c61-bbf0-aca921e0e8ae": {"__data__": {"id_": "c1c1580b-839c-4c61-bbf0-aca921e0e8ae", "embedding": null, "metadata": {"page_label": "36", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "8b56084b-33eb-4618-9d7e-940b55c064e9", "node_type": "4", "metadata": {"page_label": "36", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "8cd88a552ad5722860066279e04c65ef7377c5cc6545b35486368d9a2347bed6", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-32 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n2.5.2.10 Surface Protection \nThe material for Surface Protection on the slopes in cut and fill shall be only A -1-a, A-1-b material \naccording to AASHTO M 145 with C.B.R. not less than 30% when compacted to 95% M.D.D. The \nmaterial for Surface Protection shall also have in the respect a Plasticity Index of 4 minimum and 9 \nmaximum when determined according to AASHTO Designation T 90 and a Limit Liquid not \nexceeding 35 when determined according to AASHTO Designation T 89 \nThe material for surface protection shall be laid, compacted to an ave raged density of 98% with no \nsingle value below 96%. \n2.5.2.11 Water \nUse of sea water or brackish water shall be permitted for roadway embankment earthwork except \nfor non-load-bearing topsoil and sweet sand filling.  Also, except for water used for com paction of \nbackfill around metal pipe culverts and for subgrade and layers above, or in contact with or adjacent \nto a reinforced concrete element or structure backfill, which shall be from a source approved by the \nEngineer conforming to the requirements sp ecified for concrete mixes as per Section 4.3.8 of \nChapter 4, Concrete Works, of these Standard Specifications. \n2.5.3 Construction Requirements \nEmbankment and backfill construction consists of the placement and compaction of fill materials. \nThis work includes the following: \n1. Preparing the foundation for embankment\n2. Benching for side-hill embankments\n3. Constructing roadway embankments\n4. Constructing dikes, ramps, mounds, and berms\n5. Backfilling subexcavated areas, holes, pits, and other depressions\n6. Backfilling around structures, walls, and abutments\n7. Backfilling of manual excavation trenches\n8. Filling areas outside of the pavement prism with non-load-bearing material, topsoil, and sweet\nsand \nAll fills shall be constructed to a reasonably smooth and uniform surface and shall not vary by more \nthan 25 mm above or below the grade established and in reasonably close conformity to the lines, \ndimensions, and cross-sections shown on the Contract plans. \nAt the end of each day's operations, when rain is expected, the Contractor shall shape to drain and \nlightly compact the fill surface to a uniform cross-section. Contractor shall eliminate all ruts and low \nspots that could hold water. \n******* Embankment Construction \na. Preparing Foundation for Embankment Construction\nPreparation of foundations for embankment construction shall be done as shown on the Contract \nplans, as may be required in the Particular Specifications and as approved by the Engineer. \nPreparation of embankment foundation plans shall be included in the Contractors method statement \nas required in Section 1.15 of Chapter 1, General Requirements, of these Standard Specifications.  \nConstruction of earthwork, and in particular construction of em bankments shall not begin until this \nmethod statement is approved.  \nPreparing foundations for embankment construction includes the following: \n1. All embankments shall be cleared and grubbed in accordance with Sections 2.2 and 2.3.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3161, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "d4f634ca-7e45-4d1d-b773-991c909f7e21": {"__data__": {"id_": "d4f634ca-7e45-4d1d-b773-991c909f7e21", "embedding": null, "metadata": {"page_label": "37", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "aaad3539-51fb-4260-af6d-b263ab3b54c1", "node_type": "4", "metadata": {"page_label": "37", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "4e6ed41187b6ea649c553acbca7b31a0fe5cc24ab20f7e64faa2d1490d006581", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-33 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n2. Embankments less than 1 m high over natural ground:  Completely break up the cleared\nground surface to a minimum depth of 150 mm by ploughing or scarifying. Compact the \nground surface according to Article b of Section ******* \n3. Embankments less than 500 mm high over an existing asphalt, concrete, or gravel road\nsurface: Scarify gravel roads to a minimum depth of 150 mm. Pulverize asphalt and concrete \nroads to 200 mm below the pavement. Reduce all particles to a maximum size of 150 mm \nand produce a uniform material. Compact the surface according to Article b of Section ******* \n4. If stabilization of soft foundations is not otherwise shown on the Contract plans or required in\nthe Particular Specifications, where embankments cross ground not capable of supporting \nconstruction equipment, the Contractor shall dump successive loads of embankment material \nby type and gradation, as approved or direct ed by the Engineer, in a uniformly distributed \nlayer to construct the lower portion of the embankment on his expense. This layer shall not \nrequire compaction to a controlled density unless otherwise directed by the Engineer. \nAdditional support may be provi ded by installing sand filter blankets or geotextile /geogrid \nlayers under this layer, if directed by the Engineer. Geotextile fabric shall be used to separate \nthe fine material from the coarse material layers, meeting the material requirements of \nSection *******.  \n5. For embankments on existing slopes, including hillsides or widened existing embankments\nsteeper than a ratio of 1:3, the Contractor shall cut horizontal benches in the existing slope \nto a sufficient width to accommodate placement and compaction operations and equipment. \nSlope shall be benched as the embankment is placed and compacted in layers. Each bench \nshall begin at the intersection of the original ground and the vertical cut of the previous bench. \n6. Prior to the start of embankment construction, the Contractor shall construct a trial\nembankment section using the methodology, materials and equipment that he intends to \nutilize for the work.  Trial will be observed and tested for compliance by the Engineer, and if \nsatisfactory the Contractor shall proceed with embankment construction.  A separate trial \nsection shall be constructed for each instance where the materials, material source, \nequipment or methodology changes.  Size and location of the trial emba nkment area shall \nbe as approved by the Engineer. \n7. When required in the Contract plans , particular specifications or included in the Bills of\nQuantities, foundation preparation may include installation of prefabricated vertical drains \n(PVD).  PVDs shall be installed at the locations, spacing and to the depths as shown on the \nContract plans or required in the Particular Specifications and as may be modified by the \nEngineer, in accordance with Section *******. \nb. Rock Embankment Construction\nRock embankment construction is defined where the embankment material consists of 25 percent \nor more by volume of rock particles sized between 100 and 300 mm in diameter.  Rock fill material \nshall meet the applicable requirements of Section *******, unless otherwise approved by the \nEngineer. \nRock fill material shall be placed in horizontal layers not exceeding 300 mm in compacted thickness. \nOversized boulders or rock fragments shall be incorporated into the 300 mm layer by reducing their \nsize. Individual boulders up to a maximum of a 600 mm in diameter can be distributed within the \nembankment so as to prevent nesting and to fill in voids between the layers with finer material. Rock \nlayers with smaller rocks and blinding material shall be placed to fill these voids. Each layer shall \nalso be compacted according to Article a of Section ******* before placing the next layer. \nWhere structural pilings are to be placed in embankment locations and for embankment adjacent to \nstructures, the Contractor shall limit the maximum particle size to 100 mm. \nThe top 300 mm of the embankment with embankment material Type A-1 according to AASHTO M \n145 and Section *******.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 4230, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4fb3d105-d7c8-46ed-9aa7-9537b1cd2981": {"__data__": {"id_": "4fb3d105-d7c8-46ed-9aa7-9537b1cd2981", "embedding": null, "metadata": {"page_label": "38", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "1010dc03-8934-4630-9a9a-3c1fd0d5016b", "node_type": "4", "metadata": {"page_label": "38", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "b909edffe17d72fe81c196b919646937f9a2d6758ad3d3685abd5b8ce4daa1c7", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-34 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nc. Earth Embankment Construction \nEmbankments shall be placed in successive layers parallel to the final road surface, and the \nconstruction of wedge-shaped layers shall be restricted to the bottom layers of embankments where \nthis may be unavoidable because of cross fall, the tapering out o f embankments, or the super \nelevation of the final road surface. \nMaterials shall be placed at optimum moisture content. Dry material shall be watered and mixed as \nnecessary to obtain uniform moisture content within the optimum compaction range. Material sh all \nbe placed in uniform horizontal layers not exceeding 200 mm in depth before compaction (up to 300 \nmm maximum thick layers where trial compaction tests show the material is suitable and consistently \nmeets the compaction requirements uniformly through th e layer as otherwise specified, and if \napproved by the Engineer). Material layer shall be compacted in accordance with Article b of Section \n*******. \nPlacement of borrow materials or fills at points inaccessible to normal compaction equipment shall \nbe made in horizontal layers of loose materia l not exceeding 100 mm in depth and thoroughly \ncompacted by the use of mechanical tampers to the minimum compaction requirements included in \nArticle b of Section *******.  \n2.5.3.2 Structural Backfill \nThis work consists of backfilling against and around walls, abutments, and structures. \nBackfill material shall meet the requirements of Section *******. Where pervious material is required \nas a drainage layer backfilled adjacent to walls and abutments, it shall meet the requirements of \nSection *******. Geotextile fabric used as a filter layer between drainage layers and finer backfill \nmaterials shall meet the requirements of Section ******* and Table 2-11 \nAll structural backfill that will be under roadway or pedestrian pavement, within the roadway \nembankment prism, or where lateral and vertical bearing support is required for adjacent structures \nand utilities shall be compacted to at least 95 percent of the maximum dry density, as determined by \nthe tests described in Article c of Section *******. \nTo prevent the distortion or displacement of structures, in particular walls and abutments, the \nContractor shall place backfill evenly around all sides and parts of the structure. \nFor bridge abutments, the Contractor shall not backfill prior to placing the superstructure. After the \nsuperstructure is in place, the use of small compactors may be required to compact the backfill \naround the structure. Embankments and backfill behind the abutments shall be brought up in layers \nand compacted concurrently. Differences in backfill height against each abutment shall not exceed \n0.6 m, unless approved by the Engineer. \n******* Non-Load-Bearing Fill Placement \nWhen placing non-load-bearing, topsoil, or sweet sand material outside the staked roadway prism, \nthe Contractor shall place and grade in layers that shall not exceed a thickness of 300  mm to the \nlevels and grades as s hown on the Contract plans , or as required by the Engineer. Specific \ncompaction is not required other than what occurs during placement and natural consolidation. \nEquipment traffic and resultant compacting of non -load-bearing material placement in vegetated \nlandscape areas shall be minimized. \n******* Compacting Embankments \na. Compacting rock embankments \nAdjust the moisture content of the material to a moisture content suitable for compaction by \ndetermining the maximum density and optimum moisture content for the materials passing the 4.76 \nmm sieve per Article c of Section *******c. Compact each layer of material full width with one of the \nfollowing:  \n1. Four roller passes of a 45-metric-ton compression-type roller", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3871, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "e683987b-9321-4fba-8ef7-c6b2a6b195dc": {"__data__": {"id_": "e683987b-9321-4fba-8ef7-c6b2a6b195dc", "embedding": null, "metadata": {"page_label": "39", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "4d58af98-7f63-4338-a965-cc9198bef03f", "node_type": "4", "metadata": {"page_label": "39", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "021a063153acb63c6ed4cfc2c97b0a802d898c0cec2d4bdb6db94feee24d0bb3", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-35 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n2. Four roller passes of a vibratory roller having a minimum dynamic force of a 180-kilonewton\nimpact per vibration and a minimum frequency of 16 hertz \n3. Eight roller passes of a 20-metric-ton compression-type roller\n4. Eight roller passes of a vibratory roller having a minimum dynamic force of a 130-kilonewton\nimpact per vibration and a minimum frequency of 16 hertz \nb. Compacting Earth Embankments\nMaterials shall be classified according to AASHTO M 145 . For material classified A-1 or A-2-4, the \nContractor shall determine the maximum density according to AASHTO T 180, Method D. For other \nmaterial classifications, the Contractor shall determine the optimum moisture content and maximum \ndensity according to AASHTO T 99, Method C. \nMoisture content of the material shall be adjusted to the optimum. \nMaterial placed in all embankment layers shall conform to the requirements of Article c of Section \n******* and not more than 150mm in compacted thickness and shall be placed and compacted to at \nleast 95 percent (average of minimum 5 compaction tests) with no single value less than 93 percent \nof the maximum density. Determine the in situ density and moisture content according to  AASHTO \nT 238 and T 239 standards. \nPrior to the placement of any materials, the Contractor shall construct trial compaction tests as \ndirected by the Engineer. The material used in the trials shall be the proposed embankment material \nand the compaction equipment to be used shall be that specified and acceptable to the Engineer. \nThe objective of these trials is to determine the relationship between the number of compaction \nequipment passes and density for the proposed materials. \nc. Compaction and Moisture Control Tests\nEmbankment construction shall be tested using the procedures listed in Table 2-12. \nTable 2-12: Embankment material and compaction tests \nTest AASHTO* designation \nTesting of materials: \nSampling T 2, T 86 \nSample preparation T 87 \nSieve analysis T 27, T 88 \nLiquid limit T 89 \nPlastic limit and plasticity index T 90 \nMoisture content T 93, T 217 \nMoisture – density relationship T 180 \nSand equivalent T 176 \nSpecific gravity T 100,  T 85 \nCalifornia bearing ratio (CBR) T 193 \nClassification M 145 \nTesting degree of compaction: \nSampling T 86 \nDensity-in-place, including sand cone method T 191", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 2437, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "576fff02-5a7c-4171-b3f3-b8d04e6f7370": {"__data__": {"id_": "576fff02-5a7c-4171-b3f3-b8d04e6f7370", "embedding": null, "metadata": {"page_label": "40", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "bfb76a06-5b14-4d1e-b839-bb655dfc065e", "node_type": "4", "metadata": {"page_label": "40", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "716b38d8a232e97c73d5737d0e327c3152db8564d28dd8dce41a8e493b30cb15", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-36 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \nTest AASHTO* designation \nDensity-in-place, including drive cylinder method T 204 \nDensity-in-place, including rubber balloon method T 205 \n*American Association of State Highway and Transportation Officials (AASHTO), Washington, D.C. \n******* Prefabricated Vertical Drains \nWhen shown in the plans or included in the Bills of Quantities, the Contractor shall furnish and install \nprefabricated vertical drains (PVD).  Installation shall be in accordance with the details shown in the \nContract plans, as may be described in the part icular specifications, and as recommended by the \nprefabricated vertical drain manufacturer. \nPrior to installation of the PVDs, the Contractor shall submit details of the sequence and method of \ninstallation to the Engineer. At a minimum, this submittal shall contain the dimensions and length of \nmandrel, a detailed description of the proposed methods for overcoming obstructions, and the \nproposed methods for splicing drains. \nEquipment, methods, and materials shall be demonstrated to produce a satisfactory inst allation in \naccordance with these specifications. For this purpose, the Contractor shall be required to install trial \ndrains at different locations within the work area. \nPVDs shall be constructed prior to embankment construction. \nPrior to installation of vertical drains, a sand drainage blanket shall be placed on the ground surface \nfor use as a working platform. This platform shall have a minimum depth of 60 cm and shall consist \nof un -compacted material meeting the requirements of Section *******. Vertical drains shall be \ninstalled with equipment that disturbs subsoil minimally. A mandrel or sleeve shall be advanced \nthrough the subsoil using vibratory, constant load, or constant rate of advance methods. Mandrels \nshall have a maximum cross-sectional area of 90 cm2; shall protect the prefabricated drain material \nfrom tears, cuts, and abrasions during installation; and shall be provided with an anchor plate or rod. \nAn anchor plate or rod shall provide sufficient strength to prevent the soil from entering the bottom \nduring installation, and shall anchor the bottom of the drain at the required depth when the mandrel \nis removed. Fall ing weight impact hammers or jetting shall not be used within the compressible \nsubsoil to be drained. \nPrefabricated drains shall be installed vertically from the working surface to the required elevations \nand in a sequence that shall not require equipment to travel over previously installed drains. \nContractor shall provide the Engineer with a suitable means of verifying the plumbness of the \nequipment and determining the depth of the drain at any time. The equipment shall not deviate more \nthan 6.35 mm per metre from the vertical. \nSplices or connections in the PVD material shall be performed in a professional manner to ensure \ncontinuity of the PVD material. PVD shall be cut to leave at least 150 mm protruding above the \nworking platform at each drain location. \nWhere obstructions are encountered and cannot be penetrated, the Contractor shall abandon the \nhole. A maximum of two attempts shall be made to install a new drain within 45 cm of the obstructed \nhole. Drains that otherwise deviate from the drawing location by more than 150  mm, or that are \ndamaged or improperly installed, shall be rejected. \nWhile installing the drains, the Contractor shall consider and coordinate with any geotechnical \ninstrumentation and existing utility locations. Special care shall be taken when installing drains near \ninstrumentation and existing utilities already in place. Replacement or repair of instrumentation or \nutilities damaged by the Contractor shall be the responsibility of the Contractor.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3843, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "2db4d935-bf6b-4068-89a9-2cdfd590d888": {"__data__": {"id_": "2db4d935-bf6b-4068-89a9-2cdfd590d888", "embedding": null, "metadata": {"page_label": "41", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "fac977ee-17f9-4b6b-9480-3139769ffa81", "node_type": "4", "metadata": {"page_label": "41", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "0015e910161596cb7192f4abedad8a06296071c1530c493c7dc434dddae1fb61", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS  PART 1 – ROADS \n \n \nPage 2-37 \nCHAPTER 2: EARTHWORKS  Second Edition -SEP 2020 \n \n2.5.3.6 End Caps and Draw Lines for Ducts, Conduits, Pipe Sleeves \nand Culverts  \nThe Contractor shall furnish and install end caps for vacant ducts, conduits and pipe sleeves before \nbackfilling to prevent any intrusion of backfill material into the ends of such ducts, conduits and pipe \nsleeves. \nOpen ends of vacant pipe culverts and concrete box culverts shall be closed with bulkheads. The \nbulkheads shall be constructed with suitable concrete blocks or bricks and mortar and as approved \nby the Engineer. \nEnd caps shall be of the type and material as manufactured for respective types of ducts, conduits \nand pipe sleeves and as approved by the Engineer. \nPrior to the installation of end caps and bulkheads as specified herein, the Contractor shall furnish \nand install draw lines in all vacant ducts, conduits, pipe sleeves and culverts longer than ten metres \n(10 M). The draw lines are intended to facilitate future installation of utility cables and pipes. Draw \nlines shall consist of 6 mm dia. nylon rope as approved by the En gineer. The draw lines shall be \nsecured with a temporary fastener inside both ends of the ducts, conduits, pipe sleeves or culverts \nas approved by the Engineer. \n2.6 Subgrade Preparation \nBefore placing any paving, the Contractor shall bring the  subgrade to the required line, grade, and \ncross-section. Subgrade shall be compacted in accordance with Sections 2.6.1 and 2.6.2. A \ncompacted area shall be wide enough to allow paving machines to operate without visible distortion \nof surfacing material. \nUntil the pavement is placed, the Contractor shall maintain the subgrade in the required condition. \n2.6.1 Traffic Pavement \nTop 300 mm of the roadway embankments at the subgrade level shall be constructed in two layers \nof 150 mm each, of embankment material meeting the requirements of Section  *******, and shall \nhave a minimum density in percent of the maximum dry density of 95% with a minimum CBR of 30%. \nWhere traffic pavement is placed on existing ground or on existing subgrade, the existing material \nshall be scarified and re-compacted in accordance with Article a of Section ******* Clause 2, where \nit shall have a minimum CBR of 10 %  when compacted to 90% of MDD and a minimum CBR of 30% \nat a compaction of 95% of MDD. If the CBR of the existing material is less than 10, then the top 300 \nmm depth shall be removed and replaced with load bearing  material compacted to a minimum \ndensity of 95%, with a CBR of 30. \n2.6.2 Pedestrian Pavement \nTop 150 mm of roadway embankments under pedestrian pavement at the subgrade level shall be \ncompacted to a minimum density in percent of maximum dry density of 95%  with a CBR of 25. An \nadditional working layer of aggregate material may be placed on top of the compacted embankment \nmaterial where shown on the Contract plans or approved by the Engineer. \nWhere pedestrian pavement is placed on existing ground or on existing subgrade, the existing \nmaterial shall be scarified and recompacted in accord ance with Article a of Section ******* Clause \n2. \n2.7 Geotextile Installation   \n2.7.1 Description \nContractor shall furnish and place construction geotextile fabric, in accordance with the details shown \nin the Contract plans.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3364, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ac736d44-cba6-4ad0-9cf6-59d61d05c5f3": {"__data__": {"id_": "ac736d44-cba6-4ad0-9cf6-59d61d05c5f3", "embedding": null, "metadata": {"page_label": "42", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "e59096c7-e7ee-40d1-b3be-b0c0d2781859", "node_type": "4", "metadata": {"page_label": "42", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "9e39395c4d59a32f139078eadc0b04bf611b7fd9d9f1669ea636860a3580f396", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-38 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n2.7.2 Materials \nGeotextile materials shall meet the requirements of Section *******. \nGeotextile roll identification, storage, and handling shall conform to ASTM D 4873. During periods of \nshipment and storage, the geotextile shall be stored off the ground. Geotextile shall be covered at \nall times during shipment and storage to keep it fully protected from UV radiation, including sunlight; \nsite construction damage; precipitation; chemicals consisting of strong acids or strong bases; flames, \nincluding welding sparks; temperatures in excess of 71 o C; and any other environmental condition \nthat may damage the physical property values of the geotextile. \n2.7.3 Construction Requirements \nAreas to be covered by the geotextile shall be graded to a smooth, uniform condition free from ruts, \npotholes, and protruding objects such as rocks or sticks. Geotextile shall be spread immediately \nahead of the covering operation and shall not be left exposed to sunlight during installation for a total \nof more than 7 calendar days. \nGeotextile shall be laid smooth without excessive wrinkles. Under no circumstances shall the \ngeotextile be dragged through mud or over sharp objects that could damage it. \nSoil piles, or the manufacturer’s recommended method, shall be  used as needed to hold the \ngeotextile in place until the specified cover material is placed. \nCover material shall be placed on the geotextile and the minimum initial lift thickness required shall \nbe between the equipment tires or tracks and the geotextile at all times. Construction vehicles shall \nbe limited in size and weight to reduce rutting in the initial lift above the geotextile to not greater than \n70 mm deep to prevent overstressing. Turning of vehicles on the first lift above the geotextile shall \nnot be permitted. \nWhen placed as a measure to help stabilize soft foundations, the lift thickness covering the geotextile \nshall be 300 mm to 600 mm thick. By routing loaded haul equipment over its entire width, the \nContractor shall compact the first layer. No vibratory compaction shall be allowed on the first lift. \nShould the geotextile be torn, punctured, or the overlaps or sewn joints disturbed (as evidenced by \nvisible geotextile damage, subgrade pumping, intrusion, or roadbed distortion) the backfill aroun d \nthe damaged or displaced area shall be removed and the damaged area repaired or replaced by the \nContractor. Repair shall consist of a patch of the same type of geotextile placed over the damaged \narea. Patch shall overlap the existing geotextile from the edge of any part of the damaged area by \nthe minimum required overlap for the application. \nIf geotextile seams are to be sewn in the field or at the factory, the seams shall consist of one row of \nstitching, unless the geotextile where the seam is to be sewn  does not have a selvedge edge. If a \nselvedge edge is not present, the seams shall consist of two parallel rows of stitching. Two rows of \nstitching shall be 25 mm apart with a tolerance of ±10 mm and shall not cross except for restitching. \nSeam, stitch type, and the equipment used to perform the stitching shall be as recommended by the \nmanufacturer of the geotextile and as approved by the Engineer. \nSeams shall be sewn in such a manner that the seam can be inspected readily by the Engineer or a \nrepresentative. Seam strength may be tested and shall meet the requirements stated in Table 2-10 \nfor seam breaking strength. \n2.7.3.1 Subsurface Drainage and Soakaways \nTrench walls shall be smooth and stable. Geotextile shall be placed in a manner that ensures direct \ncontact between the soil and the geotextile (i.e., no voids, folds, or wrinkles). \nGeotextile shall either overlap a minimum of 300 mm at all longitudinal and transverse joints or the \ngeotextile joints shall be sewn. Where the trench width is less than 300 mm, the minimum overlap \nshall be the trench width. \nAn area drain is defined as a geotextile layer placed over or under a horizontal to moderately sloping \nlayer of drainage aggregate. For area drains, the geotextile shall be overlapped a minimum of 600", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 4226, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "f54a3a59-d794-4e97-b52d-381dfb578e2d": {"__data__": {"id_": "f54a3a59-d794-4e97-b52d-381dfb578e2d", "embedding": null, "metadata": {"page_label": "43", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "174a6149-519d-4cec-87b2-8631a325265a", "node_type": "4", "metadata": {"page_label": "43", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "4b9ce9c2a4fefbdcb1d5d656d40392a08931684a75922c0a602a69b29d049d4c", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-39 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \nmm at all longitudinal and transverse joints, or the geotextile joints shall be sewn. Minimum initial lift \nthickness over the geotextile in the area drain shall be 300 mm. In all cases, the upstream geotextile \nsheet shall overlap the next downstream sheet. \n2.7.3.2 Permanent Erosion Control and Ditch Lining \nGeotextile fabric used as a filter material under or behind erosion control work, such as gravel or \nrock surface sta bilisation, loose and grouted riprap and rock, gabions, ditch linings, energy \ndissipaters, and outlet protection pads, shall either be overlapped a minimum of 600  mm at all \nlongitudinal and transverse joints, or the geotextile joints shall be sewn, unless otherwise shown on \nthe Contract plans. If overlapped, the geotextile shall be placed so that the upstream strip of the \nsheet shall overlap the next downstream strip. \nPlacement of protective aggregate bedding over the top of the geotextile shall be performed where \nshown on the Contract plans and as stipulated in Section 2.7.3. \nWhen placed on slopes, each geotextile strip shall overlap the next downhill strip.  Placement of \naggregate, riprap, or other cover material on the geotextile fabric shall start at the toe of  the slope \nand proceed upwards. Geotextile fabric shall be keyed at the top and the toe of the slope as shown \nin the Contract plans. Geotextile fabric shall be secured to the slope, but shall be secured loosely \nenough so that the geotextile shall not tear when the riprap or other cover material is placed on the \nsheet. Geotextile fabric shall not be keyed at the top of the slope until the riprap or other cover \nmaterial is in place to the top of the slope. \nAll voids in the riprap or other cover material that allow the geotextile to be visible shall be backfilled \nwith coarse aggregate of 75 mm maximum size so that the geotextile is completely covered, as \ndesignated by the Engineer. When an aggregate cushion between the geotextile fabric and the \nriprap, or other  cover material, is required, it shall have a minimum thickness of 150 mm, unless \notherwise shown in the Contract plans. \nGrading of slopes after placement of the riprap, or other cover material, shall not be allowed if grading \nresults in stone movement dir ectly on the geotextile. Under no circumstances shall stones with a \nweight of more than 50 kg be allowed to roll down slope. Stones shall not be dropped from a height \ngreater than 1 m above the geotextile surface if an aggregate cushion is present, or 300 mm if a \ncushion is not present. Lower drop heights may be required if geotextile damage from the stones is \nevident and determined by the Engineer. If the geotextile fabric is placed on slopes steeper than a \nratio of 2:1, the stones shall be placed on the s lope without free fall.  Trial area to be prepared for \nsurvivability check. \n2.8 Trimming and Cleanup \n2.8.1 Description \nThis work consists of dressing and trimming all roadways improved under the Contract. This work \nextends to excavated cut slopes, compacted fill slopes, ditches, ponds, berms, and non-load-bearing \nfill areas. \n2.8.2 Construction Requirements \nContractor shall perform the following: \n1. Trim cut and fill slopes, ditches, and other fill slopes to produce smooth surfaces and uniform\ncross-sections that conform to the grades shown on the Contract plans  or set by the \nEngineer. \n2. Open and clean all channels, ditches, and gutters to ensure proper drainage.\n3. Dress the back slope of any ditch and pond berm and borrow source that shall remain\nadjacent to the roadway. Round off the top of the back slope and distribute the material evenly \nalong its base.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 3766, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "ba5bfa75-c013-4473-9522-6fe3926dedb9": {"__data__": {"id_": "ba5bfa75-c013-4473-9522-6fe3926dedb9", "embedding": null, "metadata": {"page_label": "44", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "bec400f3-5dae-4f8d-a463-8b736db3dd76", "node_type": "4", "metadata": {"page_label": "44", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "b6572de453a3512e49101069c73a77a58831f0511ac1d3ad7ecd83321d1566df", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "STANDARD CONSTRUCTION SPECIFICATIONS PART 1 – ROADS \nPage 2-40 \nCHAPTER 2: EARTHWORKS Second Edition -SEP 2020 \n4. Grade and smooth to uniform slopes and to the gra des shown on the Contract plans or set\nby the Engineer. Also, grade and smooth any natural areas within the roadway corridor that \nare not otherwise designated not to be disturbed and protected as required by the Engineer. \n5. Remove and dispose of all weeds, b rush, refuse, and debris that lie within the roadway\ncorridor. \n6. Remove from shoulders all loose rocks and gravel.\n7. Distribute evenly along the embankment slope any material not needed to bring the\nembankment fill slope to the required cross-section. \nContractor shall not perform the following: \n1. Use heavy equipment, such as tractors, graders, etc., to trim the embankment surface of an\nexisting or new bituminous surface. \n2. Drag, push, or scrape any material across completed surfacing or pavement.\nWhen the Contract requires the Contractor to rebuild part of a roadway, only the rebuilt areas shall \nbe trimmed and cleaned up. If the Contractor’s work obstructs ditches or side roads, they shall be \ncleared and the debris disposed of as the Engineer directs. \nTrimming and cleanup work will be inspected by the Engineer, and final acceptance is subject to the \nContractor’s approval.", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1323, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}, "4c788242-3e8c-4113-8ea9-f9baaf5f000b": {"__data__": {"id_": "4c788242-3e8c-4113-8ea9-f9baaf5f000b", "embedding": null, "metadata": {"page_label": "45", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "excluded_embed_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "excluded_llm_metadata_keys": ["file_name", "file_type", "file_size", "creation_date", "last_modified_date", "last_accessed_date"], "relationships": {"1": {"node_id": "ea22cb8e-6005-4e58-a102-fee67ed9d335", "node_type": "4", "metadata": {"page_label": "45", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}, "hash": "07ead61817eac2087d4e7a8583a89e2e76bca1777cfa52997d6b8567f489a728", "class_name": "RelatedNodeInfo"}}, "metadata_template": "{key}: {value}", "metadata_separator": "\n", "text": "Chapter 2: Earthworks \nPage 2-41 \nINDEX \nbackfill, 2-24 \nboreholes, 2-4 \nborrow excavation, 2-21, 2-24 \nBorrow excavation. See roadway excavation \ncleanup, 2-40 \nclearing, 2-7 \ncompacting earth. See compacting \nembankments \ncompacting embankments, 2-35 \ncompacting rock. See compacting \nembankments \ncompaction control tests, 2-35 \ndewatering, 2-13 \ndisposal of material, 2-9 \ndisposal of materials, 2-11 \ndisposal of unsuitable or excess material, 2-\n20 \nditch lining. See erosion control \nearth embankment construction, 2-34 \nearthworks, 2-1 \nembankment construction, 2-33 \nembankment material, 2-24 \nembankments, 2-24 \nerosion control, 2-39 \nexcavation, 2-11 \nexcavation support, 2-22 \ngeosynthetic installation, 2-38 \ngeosynthetics, 2-29 \ngeotextile fabric. See geosynthetics \ngrubbing. See clearing \nhauling, 2-5, 2-12 \nmanual excavation, 2-23 \nmoisture control tests, 2-35 \nnon-load-bearing fill placement, 2-34 \nnon-load-bearing material, 2-27 \npervious backfill, 2-26 \nprefabricated vertical drains, 2-28, 2-36, 2-37 \nremoval of structures, 2-9 \nroadside cleanup, 2-9 \nroadway excavation. See excavation \nrock blasting. See rock cuts \nrock cuts, 2-15 \nrock embankment construction, 2-33 \nsand, 2-27 \nsoakaways. See underground drainage \nstructural backfill, 2-25, 2-34 \nstructural excavation, 2-21 \nStructural excavation. See roadway \nexcavation \nsubexcavation, 2-20 \nSubexcavation. See roadway excavation \nsubgrade, 2-37 \nsweet sand material. See non-load bearing \ntopsoil, 2-8, 2-28 \nunderground drainage, 2-39 \nutility coordination, 2-23 \nwater, 2-32", "mimetype": "text/plain", "start_char_idx": 0, "end_char_idx": 1561, "metadata_seperator": "\n", "text_template": "{metadata_str}\n\n{content}", "class_name": "TextNode"}, "__type__": "1"}}, "docstore/ref_doc_info": {"e8d164ca-e06a-431d-b298-17467a15ca19": {"node_ids": ["9610743f-c976-44d9-a0fd-************"], "metadata": {"page_label": "1", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "62c0a158-9508-48c7-a1bd-d4b5e0c305e1": {"node_ids": ["877b6830-713c-45a3-b987-c6182194ae98", "6a531744-c165-4fee-83cf-fd7c5f5e694e"], "metadata": {"page_label": "2", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "03cec759-3cf1-4617-8e4f-605ba8a97277": {"node_ids": ["d988cfa9-5740-496f-ace6-4a1051cfaa06"], "metadata": {"page_label": "3", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "5a977cf6-5483-45c6-b9d4-d8e024132da9": {"node_ids": ["970c0f80-f557-4cb2-a835-2418ff55783b"], "metadata": {"page_label": "4", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "d2917da7-ba95-4753-b09b-51b0ca388b34": {"node_ids": ["44ba16ec-3403-4b9d-82ab-46b45c226ca6"], "metadata": {"page_label": "5", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "07f452a3-18ac-4314-b38a-bf19c9c9a3c8": {"node_ids": ["ad5bbaba-4c13-4aa0-99e2-bb9034d43169"], "metadata": {"page_label": "6", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "d9eae386-5f7d-49d3-a479-5f1d1fccc254": {"node_ids": ["d9ea4a07-354b-41b4-b414-752c92ea04d0"], "metadata": {"page_label": "7", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "6e50b4db-a758-4431-b1a4-2e74cbc5fc72": {"node_ids": ["41455b7e-5d66-4cfe-a037-ac33ac210cb9"], "metadata": {"page_label": "8", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "61ac8878-6b6f-4039-8b3c-5557469478fb": {"node_ids": ["d2b1e02f-7084-45ad-9840-9775c31c2add"], "metadata": {"page_label": "9", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "4adf43d7-2a09-48df-a35d-5aad35dd8e00": {"node_ids": ["999b634a-3943-45f5-9de2-84c80346b14f"], "metadata": {"page_label": "10", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "a13ce568-687d-4fc0-b2bd-03c2dad7e14e": {"node_ids": ["8007dc05-096c-4966-878a-a60341eceaa0"], "metadata": {"page_label": "11", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "327f3902-62b6-4229-b672-d35ad99911a2": {"node_ids": ["4f31f787-9d6c-43db-8c68-d8c6962b587a"], "metadata": {"page_label": "12", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "9624ec0a-d0fb-44af-b755-1304ee3c73ee": {"node_ids": ["4fbbcdfc-bb7c-451b-81e3-21f9353b88cc"], "metadata": {"page_label": "13", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "7b3730fa-fb3e-4721-9760-f2d2f9fddddc": {"node_ids": ["d2907336-9ca0-4eab-b643-0efba4c349a3"], "metadata": {"page_label": "14", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "450f1d04-7baa-4d39-a638-4a709ea085dd": {"node_ids": ["51ce4602-bdda-4da5-8416-29d3358d54bb"], "metadata": {"page_label": "15", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "9010b59d-6d1a-40f8-a336-82bf3ea93e5b": {"node_ids": ["71806e9f-825d-4ced-967f-3243bd8cbbe5"], "metadata": {"page_label": "16", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "a99437ac-950d-4aad-b8b9-c9a52f997299": {"node_ids": ["909a2b85-8707-4c86-a23f-309cd3d65ef6"], "metadata": {"page_label": "17", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "8e3d120d-68ed-450f-a3a2-f430922d0e14": {"node_ids": ["f069fdc4-b5d7-4efa-ad8f-6d0d478669e6"], "metadata": {"page_label": "18", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "6c3e05a0-4311-43af-8b46-ba7532917d6f": {"node_ids": ["f757713e-1630-4126-a2b6-60511ae91723"], "metadata": {"page_label": "19", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "2144aefc-b399-4ea0-8ec9-bb6324e8d1eb": {"node_ids": ["87272613-af6e-4abe-987c-ad71e3d10e79"], "metadata": {"page_label": "20", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "a1ee8109-5f8f-4e2e-b736-686264825d8d": {"node_ids": ["498301b9-cf96-4423-8679-46efbf76f488"], "metadata": {"page_label": "21", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "6a6bcc5a-0bd4-407a-9d74-718427c79c19": {"node_ids": ["1fa16851-f4c4-4702-8edb-908f25bb0de3"], "metadata": {"page_label": "22", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "dc47a7a1-14b8-42e1-b66f-2b2837ecd5b4": {"node_ids": ["da6d4d86-5e1d-404f-acfb-1c8d929c1a69"], "metadata": {"page_label": "23", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "4f871724-bd44-499a-af26-6fb0fe053ef3": {"node_ids": ["caf7b161-f106-43cb-bd7f-ffb3e41f5392"], "metadata": {"page_label": "24", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "fcce2a2f-7f38-4f2a-943a-67506fa80ae0": {"node_ids": ["ad28c9c3-9998-47c6-84a0-d7935d58cace"], "metadata": {"page_label": "25", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "df95b1c3-14a4-451f-8c6c-883a9ed62b67": {"node_ids": ["8422aec1-ba6a-4e9a-839d-64c5af06b066"], "metadata": {"page_label": "26", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "e56fbb7d-f5f4-4147-b65e-9cf7630dafd4": {"node_ids": ["af0e4d59-1c51-41d6-8195-b361de7781ec"], "metadata": {"page_label": "27", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "aaa61bd5-f7d2-43c2-84f5-a093410638aa": {"node_ids": ["bbe148aa-4808-4139-a452-a9a9b99aacec"], "metadata": {"page_label": "28", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "43e75014-6d93-4c1a-a869-e26d6df8aac2": {"node_ids": ["c6da6039-6f7d-4af8-8515-2e4fb76f2dd1"], "metadata": {"page_label": "29", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "2ff2f7bd-3727-4bc7-8c10-6b4f8e764bd0": {"node_ids": ["910013af-97b4-4dca-a7c6-28a5a315b595"], "metadata": {"page_label": "30", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "bf50f4ad-d371-415b-bd50-13f665f99ec3": {"node_ids": ["3e7de4a9-2b51-47b1-9802-b15ca7eb102f"], "metadata": {"page_label": "31", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "e350bcb1-e8e7-4ba2-b32b-63ac62c39a39": {"node_ids": ["8abf17de-2e47-4ab0-a121-f0b9b4e2abbb"], "metadata": {"page_label": "32", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "af107a73-6b56-458a-9cc6-9c6ae0e4271e": {"node_ids": ["e9d58db3-f5e9-427d-bc47-ebca4ddee3c4"], "metadata": {"page_label": "33", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "492ee8a7-4a31-4f2b-99fb-f87a7c95f908": {"node_ids": ["1dfaa977-14d9-413d-85d9-b7b81dbcdf28"], "metadata": {"page_label": "34", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "2476d2f1-5efb-417d-a974-c73b2c9b9198": {"node_ids": ["5e742965-a09b-46e3-afb4-3b70adcf4d20"], "metadata": {"page_label": "35", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "8b56084b-33eb-4618-9d7e-940b55c064e9": {"node_ids": ["c1c1580b-839c-4c61-bbf0-aca921e0e8ae"], "metadata": {"page_label": "36", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "aaad3539-51fb-4260-af6d-b263ab3b54c1": {"node_ids": ["d4f634ca-7e45-4d1d-b773-991c909f7e21"], "metadata": {"page_label": "37", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "1010dc03-8934-4630-9a9a-3c1fd0d5016b": {"node_ids": ["4fb3d105-d7c8-46ed-9aa7-9537b1cd2981"], "metadata": {"page_label": "38", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "4d58af98-7f63-4338-a965-cc9198bef03f": {"node_ids": ["e683987b-9321-4fba-8ef7-c6b2a6b195dc"], "metadata": {"page_label": "39", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "bfb76a06-5b14-4d1e-b839-bb655dfc065e": {"node_ids": ["576fff02-5a7c-4171-b3f3-b8d04e6f7370"], "metadata": {"page_label": "40", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "fac977ee-17f9-4b6b-9480-3139769ffa81": {"node_ids": ["2db4d935-bf6b-4068-89a9-2cdfd590d888"], "metadata": {"page_label": "41", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "e59096c7-e7ee-40d1-b3be-b0c0d2781859": {"node_ids": ["ac736d44-cba6-4ad0-9cf6-59d61d05c5f3"], "metadata": {"page_label": "42", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "174a6149-519d-4cec-87b2-8631a325265a": {"node_ids": ["f54a3a59-d794-4e97-b52d-381dfb578e2d"], "metadata": {"page_label": "43", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "bec400f3-5dae-4f8d-a463-8b736db3dd76": {"node_ids": ["ba5bfa75-c013-4473-9522-6fe3926dedb9"], "metadata": {"page_label": "44", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}, "ea22cb8e-6005-4e58-a102-fee67ed9d335": {"node_ids": ["4c788242-3e8c-4113-8ea9-f9baaf5f000b"], "metadata": {"page_label": "45", "file_name": "a.pdf", "file_path": "D:\\coding\\pyhton\\documents\\a.pdf", "file_type": "application/pdf", "file_size": 413379, "creation_date": "2025-06-02", "last_modified_date": "2025-06-03"}}}}
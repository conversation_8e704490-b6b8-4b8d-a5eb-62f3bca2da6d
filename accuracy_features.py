# Accuracy and Precision Features
import re
import hashlib
from datetime import datetime
from config import RAG_CONFIG, TECHNICAL_PATTERNS

def extract_keywords_from_query(query):
    """Extract keywords from query for precision search"""
    # Remove common stop words and extract meaningful terms
    stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'how', 'many', 'times', 'is', 'are', 'was', 'were'}
    
    # Extract technical patterns first
    keywords = []
    for pattern in TECHNICAL_PATTERNS:
        matches = re.findall(pattern, query, re.IGNORECASE)
        keywords.extend(matches)
    
    # Extract other meaningful words (3+ characters, not stop words)
    words = re.findall(r'\b\w{3,}\b', query.lower())
    keywords.extend([word for word in words if word not in stop_words])
    
    return list(set(keywords))

def find_all_occurrences(text, keyword):
    """Find all occurrences of keyword in text"""
    matches = []
    start = 0
    while True:
        pos = text.find(keyword, start)
        if pos == -1:
            break
        matches.append(pos)
        start = pos + 1
    return matches

def find_fuzzy_matches(text, keyword, threshold=0.8):
    """Find fuzzy matches using simple similarity"""
    matches = []
    words = text.split()
    
    for i, word in enumerate(words):
        # Simple similarity check
        similarity = calculate_similarity(word.lower(), keyword.lower())
        if similarity >= threshold:
            pos = text.find(word)
            matches.append({
                'position': pos,
                'confidence': similarity,
                'matched_text': word
            })
    
    return matches

def calculate_similarity(str1, str2):
    """Calculate simple string similarity"""
    if str1 == str2:
        return 1.0
    
    # Simple character-based similarity
    longer = str1 if len(str1) > len(str2) else str2
    shorter = str2 if len(str1) > len(str2) else str1
    
    if len(longer) == 0:
        return 1.0
    
    # Count matching characters
    matches = sum(1 for a, b in zip(longer, shorter) if a == b)
    return matches / len(longer)

def generate_case_variants(keyword):
    """Generate case variants of a keyword"""
    variants = [
        keyword.upper(),
        keyword.lower(),
        keyword.title(),
        keyword.capitalize()
    ]
    return list(set(variants))

def precise_keyword_search(keyword_index, query_keywords, case_sensitive=False):
    """Perform ultra-precise keyword search with multiple matching strategies"""
    results = []
    
    for filename, sentences in keyword_index.items():
        for sent_data in sentences:
            sentence = sent_data['text']
            search_text = sentence if case_sensitive else sentence.lower()
            
            for keyword in query_keywords:
                search_keyword = keyword if case_sensitive else keyword.lower()
                
                # 1. Exact string matching
                exact_matches = find_all_occurrences(search_text, search_keyword)
                for match_pos in exact_matches:
                    results.append({
                        'filename': filename,
                        'sentence_idx': sent_data['sentence_idx'],
                        'keyword': keyword,
                        'match_position': match_pos,
                        'context': sentence,
                        'start_pos': sent_data['start_pos'],
                        'confidence': 1.0,
                        'match_type': 'exact'
                    })
                
                # 2. Fuzzy matching for variations (if enabled)
                if RAG_CONFIG.get("enable_fuzzy_matching", False):
                    fuzzy_matches = find_fuzzy_matches(search_text, search_keyword)
                    for match_info in fuzzy_matches:
                        results.append({
                            'filename': filename,
                            'sentence_idx': sent_data['sentence_idx'],
                            'keyword': keyword,
                            'match_position': match_info['position'],
                            'context': sentence,
                            'start_pos': sent_data['start_pos'],
                            'confidence': match_info['confidence'],
                            'match_type': 'fuzzy',
                            'matched_text': match_info['matched_text']
                        })
                
                # 3. Case variants (if enabled)
                if RAG_CONFIG.get("enable_case_variants", False):
                    case_variants = generate_case_variants(keyword)
                    for variant in case_variants:
                        if variant.lower() != search_keyword:
                            variant_matches = find_all_occurrences(sentence, variant)
                            for match_pos in variant_matches:
                                results.append({
                                    'filename': filename,
                                    'sentence_idx': sent_data['sentence_idx'],
                                    'keyword': keyword,
                                    'match_position': match_pos,
                                    'context': sentence,
                                    'start_pos': sent_data['start_pos'],
                                    'confidence': 0.95,
                                    'match_type': 'case_variant',
                                    'matched_text': variant
                                })
    
    return results

def perform_precision_search(query, keyword_index):
    """Perform comprehensive precision search"""
    # Extract keywords from query
    query_keywords = extract_keywords_from_query(query)
    
    if not query_keywords:
        return {
            'keyword_count': 0,
            'keyword_breakdown': {},
            'locations': {},
            'exact_matches': []
        }
    
    # Perform precise search
    exact_matches = precise_keyword_search(keyword_index, query_keywords)
    
    # Organize results
    keyword_breakdown = {}
    locations = {}
    
    for match in exact_matches:
        keyword = match['keyword']
        
        # Count occurrences
        if keyword not in keyword_breakdown:
            keyword_breakdown[keyword] = 0
            locations[keyword] = []
        
        keyword_breakdown[keyword] += 1
        
        # Store location info
        locations[keyword].append({
            'filename': match['filename'],
            'sentence_idx': match['sentence_idx'],
            'context': match['context'],
            'confidence': match['confidence'],
            'match_type': match.get('match_type', 'exact')
        })
    
    total_count = sum(keyword_breakdown.values())
    
    return {
        'keyword_count': total_count,
        'keyword_breakdown': keyword_breakdown,
        'locations': locations,
        'exact_matches': exact_matches,
        'query_keywords': query_keywords
    }

def expand_query_for_better_matching(query):
    """Expand query with synonyms and related terms for better matching"""
    if not RAG_CONFIG.get("enable_query_expansion", False):
        return query
    
    # Simple query expansion - add common synonyms
    expansions = {
        'standard': ['specification', 'requirement', 'guideline'],
        'test': ['testing', 'examination', 'evaluation'],
        'method': ['procedure', 'process', 'technique'],
        'material': ['substance', 'component', 'element'],
        'section': ['clause', 'part', 'subsection'],
        'table': ['chart', 'figure', 'diagram']
    }
    
    expanded_terms = []
    words = query.lower().split()
    
    for word in words:
        if word in expansions:
            expanded_terms.extend(expansions[word])
    
    if expanded_terms:
        expansion_text = " OR " + " OR ".join(expanded_terms)
        return query + expansion_text
    
    return query

def get_accuracy_features():
    """Get list of enabled accuracy features for display"""
    features = []
    
    if RAG_CONFIG.get("enable_reranking", False):
        features.append("🎯 LLM Reranking")
    if RAG_CONFIG.get("enable_hybrid_search", False):
        features.append("🔍 Hybrid Search")
    if RAG_CONFIG.get("enable_query_expansion", False):
        features.append("📈 Query Expansion")
    if RAG_CONFIG.get("enable_metadata_filtering", False):
        features.append("📊 Metadata Filtering")
    if RAG_CONFIG.get("sentence_splitter", False):
        features.append("🎯 Semantic Splitting")
    if RAG_CONFIG.get("enable_exact_keyword_search", False):
        features.append("🔍 Exact Keyword Search")
    if RAG_CONFIG.get("enable_keyword_index", False):
        features.append("📇 Keyword Index")
    if RAG_CONFIG.get("enable_full_text_search", False):
        features.append("📄 Full Text Search")
    if RAG_CONFIG.get("enable_fuzzy_matching", False):
        features.append("🔍 Fuzzy Matching")
    if RAG_CONFIG.get("enable_case_variants", False):
        features.append("🔤 Case Variants")
    if RAG_CONFIG.get("enable_ngram_index", False):
        features.append("📝 N-gram Index")
    if RAG_CONFIG.get("enable_context_compression", False):
        features.append("🗜️ Context Compression")
    if RAG_CONFIG.get("enable_query_caching", False):
        features.append("🚀 Query Caching")
    if RAG_CONFIG.get("enable_user_feedback", False):
        features.append("📝 User Feedback")
    if RAG_CONFIG.get("enable_adaptive_learning", False):
        features.append("🧠 Adaptive Learning")
    
    return features

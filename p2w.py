import os
import faiss
import json
import datetime
import streamlit as st
import pandas as pd
import pdfplumber
import camelot
from langchain.embeddings import OpenAIEmbeddings
from langchain.vectorstores import FAISS
import xml.etree.ElementTree as ET

# Setup Directories
FAISS_DIR = "faiss_indices"
IMAGE_DIR = "extracted_images"
os.makedirs(FAISS_DIR, exist_ok=True)
os.makedirs(IMAGE_DIR, exist_ok=True)

# 1️⃣ FAISS Index Management (Now Saves Properly!)
def create_project_index(project_name, texts):
    version = datetime.datetime.now().strftime("%Y%m%d_%H%M%S") 
    index_file = f"{FAISS_DIR}/faiss_{project_name}_v{version}.bin"
    
    embeddings = OpenAIEmbeddings(model="text-embedding-ada-002")
    vector_store = FAISS.from_texts(texts, embeddings)

    faiss.write_index(vector_store.index, index_file)
    update_version_history(project_name, version)
    print(f"\n✅ FAISS index saved for project: {project_name}, version: {version}")

    return vector_store

def load_faiss_index(project_name, version="latest"):
    if version == "latest":
        index_files = sorted([f for f in os.listdir(FAISS_DIR) if f.startswith(f"faiss_{project_name}_v")], reverse=True)
        if index_files:
            latest_version = index_files[0].split("_v")[1].replace(".bin", "")
            return load_faiss_index_by_version(project_name, latest_version)
    
    return load_faiss_index_by_version(project_name, version)

def load_faiss_index_by_version(project_name, version):
    index_file = f"{FAISS_DIR}/faiss_{project_name}_v{version}.bin"
    
    if os.path.exists(index_file):
        index = faiss.read_index(index_file)
        vector_store = FAISS(index=index, embedding_function=OpenAIEmbeddings())
        return vector_store
    return None

def update_version_history(project_name, version):
    history_file = f"{FAISS_DIR}/{project_name}_faiss_history.json"
    
    history = []
    if os.path.exists(history_file):
        with open(history_file, "r", encoding="utf-8") as file:
            history = json.load(file)

    history.append({"version": version, "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")})

    with open(history_file, "w", encoding="utf-8") as file:
        json.dump(history, file, indent=4)

# 2️⃣ PDF Table & Figure Extraction (Now Exports Data Properly!)
def extract_tables(pdf_path):
    tables = camelot.read_pdf(pdf_path, pages="all", flavor="stream")
    extracted_data = [table.df for table in tables]
    return extracted_data

def export_to_csv(tables):
    combined_df = pd.concat(tables)
    combined_df.to_csv("tables.csv", index=False)
    print("\n✅ Tables exported to: tables.csv")

def export_to_json(tables):
    json_data = [table.to_dict(orient="records") for table in tables]
    with open("tables.json", "w", encoding="utf-8") as file:
        json.dump(json_data, file, indent=4)
    print("\n✅ Tables exported to: tables.json")

def export_to_xml(tables):
    root = ET.Element("Tables")
    for idx, table in enumerate(tables):
        table_element = ET.SubElement(root, f"Table_{idx}")
        for row in table.to_dict(orient="records"):
            row_element = ET.SubElement(table_element, "Row")
            for key, value in row.items():
                cell = ET.SubElement(row_element, key)
                cell.text = str(value)
    tree = ET.ElementTree(root)
    tree.write("tables.xml")
    print("\n✅ Tables exported to: tables.xml")

def extract_figures(pdf_path):
    doc = pdfplumber.open(pdf_path)
    for page_num, page in enumerate(doc.pages):
        images = page.images
        for img_idx, img in enumerate(images):
            img_path = os.path.join(IMAGE_DIR, f"page_{page_num}_img_{img_idx}.png")
            with open(img_path, "wb") as img_file:
                img_file.write(img["stream"].getvalue())

    print("\n✅ Figures saved to extracted_images folder!")

# 3️⃣ FAISS Version Management Dashboard (Now Shows Correct History!)
def display_version_table(project_name):
    history_file = f"{FAISS_DIR}/{project_name}_faiss_history.json"
    
    if not os.path.exists(history_file):
        st.warning("No FAISS versions found for this project.")
        return

    with open(history_file, "r", encoding="utf-8") as file:
        history = json.load(file)

    df = pd.DataFrame(history)
    st.dataframe(df)

def restore_faiss_version(project_name, version):
    index_file = f"{FAISS_DIR}/faiss_{project_name}_v{version}.bin"
    latest_index_file = f"{FAISS_DIR}/faiss_{project_name}_latest.bin"

    if os.path.exists(index_file):
        os.rename(index_file, latest_index_file)
        st.success(f"✅ FAISS version {version} restored as latest.")
    else:
        st.error("⚠️ Version not found.")

st.title("📊 FAISS Version Dashboard")
project_name = st.text_input("Enter project name:")

if project_name:
    display_version_table(project_name)
    version = st.text_input("Enter version to restore:")
    if st.button("Restore Version"):
        restore_faiss_version(project_name, version)
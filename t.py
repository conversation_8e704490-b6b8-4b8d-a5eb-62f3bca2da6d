from openai import OpenAI
from dotenv import load_dotenv
import os

# Load API key from .env file
load_dotenv()
api_key = os.getenv("OPENAI_API_KEY")

# Initialize OpenAI client
client = OpenAI(api_key=api_key)

# Function to test ChatGPT response
def test_chatgpt():
    try:
        response = client.chat.completions.create(
            model="gpt-4.1-nano",  # Cheapest available model
            messages=[{"role": "user", "content": "Summarize this text: Artificial Intelligence is transforming industries worldwide."}],
            max_tokens=100
        )
        print("Response from ChatGPT:")
        print(response.choices[0].message.content)
    except Exception as e:
        print("Error:", e)

# Run the test
test_chatgpt()
# RAG Configuration and Settings
import os

# Setup directories
DATA_DIR = "documents"
CHROMA_DB_DIR = "chroma_db"
CACHE_DIR = "cache"
CHAT_HISTORY_FILE = "chat_history.json"
METADATA_FILE = "document_metadata.json"
FEEDBACK_FILE = "user_feedback.json"
QUERY_CACHE_FILE = os.path.join(CACHE_DIR, "query_cache.json")
RESPONSE_CACHE_FILE = os.path.join(CACHE_DIR, "response_cache.json")
LEARNING_DATA_FILE = os.path.join(CACHE_DIR, "learning_data.json")

# Create directories
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(CHROMA_DB_DIR, exist_ok=True)
os.makedirs(CACHE_DIR, exist_ok=True)

# ULTRA-PRECISION RAG Configuration for MAXIMUM ACCURACY
RAG_CONFIG = {
    # Ultra-Precise Chunking Strategy
    "chunk_size": 400,          # Much larger chunks for more context
    "chunk_overlap": 200,       # 50% overlap - still ensures good coverage
    "sentence_splitter": True,  # Use sentence-aware splitting
    "paragraph_aware": True,    # Preserve paragraph boundaries
    
    # Maximum Recall Parameters
    "similarity_top_k": 20,     # Much more candidates for comprehensive recall
    "similarity_cutoff": 0.1,   # Lower cutoff for maximum recall
    "rerank_top_n": 10,         # Re-rank more candidates
    
    # Advanced Processing
    "response_mode": "tree_summarize",
    "streaming": False,
    "enable_reranking": True,
    "enable_hybrid_search": True,
    "enable_query_expansion": True,
    "enable_exact_keyword_search": True,
    "enable_page_tracking": True,
    "enable_fuzzy_matching": True,      # NEW: Fuzzy string matching
    "enable_case_variants": True,       # NEW: Case-insensitive variants
    
    # Multi-level Indexing
    "enable_hierarchical": True,
    "enable_metadata_filtering": True,
    "enable_tree_index": True,
    "enable_keyword_index": True,
    "enable_ngram_index": True,         # NEW: N-gram indexing for partial matches
    "enable_phonetic_search": True,    # NEW: Phonetic matching
    
    # Enhanced Conversational Memory
    "context_window": 8,               # Much more conversation context
    "max_context_messages": 50,        # Maximum messages to keep
    "context_token_limit": 8000,       # Token limit for context
    "enable_chat_memory": True,
    "enable_context_compression": True, # NEW: Compress old context
    "context_warning_threshold": 40,   # Warn when approaching limit
    
    # Ultra-Quality Control
    "min_chunk_size": 20,      # Smaller minimum for keyword capture
    "max_chunk_size": 250,     # Smaller maximum for precision
    "quality_threshold": 0.01, # Ultra-low threshold for keyword chunks
    
    # Ultra-Precision Features
    "enable_full_text_search": True,
    "enable_regex_search": True,
    "enable_multi_pattern_search": True, # NEW: Multiple search patterns
    "enable_context_expansion": True,    # NEW: Expand context around matches
    "preserve_formatting": True,
    "enable_cross_reference": True,     # NEW: Cross-reference validation
    "enable_duplicate_detection": True,  # NEW: Detect and count duplicates
    
    # Caching & Learning Features
    "enable_query_caching": True,        # NEW: Cache query results
    "enable_response_caching": True,     # NEW: Cache responses
    "enable_user_feedback": True,        # NEW: User feedback system
    "enable_adaptive_learning": True,    # NEW: Learn from feedback
    "cache_ttl_hours": 24,              # Cache time-to-live
    "feedback_learning_rate": 0.1       # Learning rate for feedback
}

# Technical patterns for exact matching
TECHNICAL_PATTERNS = [
    r'\b[A-Z]{2,}\s+[A-Z]?\s*\d+\b',  # AASHTO T 27, ASTM D 123, etc.
    r'\b[A-Z]{2,}-\d+\b',             # ISO-123, ANSI-456, etc.
    r'\bSection\s+\d+(?:\.\d+)*\b',   # Section 3.2.1
    r'\bTable\s+\d+(?:\.\d+)*\b',     # Table 5.1
    r'\bFigure\s+\d+(?:\.\d+)*\b',    # Figure 2.3
    r'\bAppendix\s+[A-Z]\b',          # Appendix A
    r'\bChapter\s+\d+\b',             # Chapter 5
    r'\bClause\s+\d+(?:\.\d+)*\b',    # Clause 4.2.1
]

# Supported file types
SUPPORTED_FILE_TYPES = [
    ".pdf", ".txt", ".docx", ".doc", ".rtf", ".odt",
    ".html", ".htm", ".md", ".csv", ".xlsx", ".xls"
]

# Default system prompt for LLM
DEFAULT_SYSTEM_PROMPT = """You are a helpful AI assistant that answers questions using both the provided document context and your general knowledge.

Key Instructions:
1. FIRST, check if the question can be answered using the provided document context
2. If the document context contains relevant information, use it and cite the source documents
3. If the document context doesn't contain relevant information, use your general knowledge to provide a helpful answer
4. For simple questions (like math, general knowledge), feel free to answer directly
5. For document-specific questions (like "what's the title", "summarize this"), focus on the provided context
6. Always be helpful and provide useful responses rather than saying you cannot answer
7. Remember and reference previous parts of our conversation when relevant
8. Maintain conversation flow by referencing earlier topics when appropriate

Format your responses clearly and be as helpful as possible."""
